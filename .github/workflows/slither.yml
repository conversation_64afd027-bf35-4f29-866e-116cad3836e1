name: Slither Analysis

on:
  merge_group:
  pull_request:
  push:
    branches:
      - main
      - release/**
    tags:
      - "*"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref_name || github.sha }}
  cancel-in-progress: true

jobs:
  analyze:
    # Takes about 2 minutes
    timeout-minutes: 10
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: crytic/slither-action@v0.4.0
        with:
          fail-on: medium
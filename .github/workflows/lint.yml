name: Solhint

on:
  merge_group:
  pull_request:
  push:
    branches:
      - main
      - release/**
    tags:
      - "*"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref_name || github.sha }}
  cancel-in-progress: true

jobs:
  lint:
    # Usually done in 30 seconds
    timeout-minutes: 5
    strategy:
      fail-fast: true

    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20' # LTS till Oct-25

      - name: Clear npm cache
        run: npm cache clean --force

      - name: Install Solhint
        run: npm install --save-dev solhint

      - name: Run Solhint
        run: |
          npx solhint 'src/**/*.sol' -c ./src/.solhint.json

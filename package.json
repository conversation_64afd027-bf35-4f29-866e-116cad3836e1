{"name": "imua-contracts", "version": "1.0.0", "description": "The imua-contracts repository contains a set of smart contracts deployed on both Imuachain and the target client chains, which facilitate assets deposit and withdrawal, cross-chain communication, and restaking operations for native assets and liquid staking tokens (LSTs), ensuring secure interactions and efficient management of restaked assets.", "main": "hardhat.config.js", "directories": {"doc": "docs", "lib": "lib", "test": "test"}, "scripts": {"test": "hardhat test", "deploy:utxogateway:localnet": "hardhat run script/hardhat/deploy-and-setup-utxogateway.script.js --network imuachain_localnet", "deploy:utxogateway:testnet": "hardhat run script/hardhat/deploy-and-setup-utxogateway.script.js --network imuachain_testnet"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@cosmjs/proto-signing": "^0.32.4", "@cosmjs/stargate": "^0.32.2", "@evmos/address-converter": "^0.1.9", "@evmos/evmosjs": "github:evmos/evmosjs#main", "@evmos/proto": "^0.2.1", "@nomicfoundation/hardhat-foundry": "^1.1.3", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@types/chai": "^4.3.20", "@types/mocha": "^10.0.10", "@types/node": "^22.13.5", "axios": "^1.7.9", "bip39": "^3.1.0", "bitcoinjs-lib": "^6.1.5", "chai": "^4.5.0", "dotenv": "^16.4.7", "ecpair": "^2.1.0", "esm": "^3.2.25", "ethereumjs-wallet": "^1.0.2", "hardhat": "^2.22.17", "mocha": "^10.2.0", "solhint": "^5.0.1", "tiny-secp256k1": "^2.2.3", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "dependencies": {"@openzeppelin/upgrades-core": "^1.40.0", "ethers": "^6.9.0", "hardhat-gas-reporter": "^1.0.9", "ripple-address-codec": "^5.0.0", "xrpl": "^4.3.0"}, "optionalDependencies": {"fsevents": "^2.3.3"}}
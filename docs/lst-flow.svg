<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="1005px" preserveAspectRatio="none" style="width:2775px;height:1005px;background:#FFFFFF;" version="1.1" viewBox="0 0 2775 1005" width="2775px" zoomAndPan="magnify"><defs/><g><line style="stroke:#181818;stroke-width:0.5;stroke-dasharray:5.0,5.0;" x1="24" x2="24" y1="81.2969" y2="924.2188"/><line style="stroke:#181818;stroke-width:0.5;stroke-dasharray:5.0,5.0;" x1="504.5" x2="504.5" y1="81.2969" y2="924.2188"/><line style="stroke:#181818;stroke-width:0.5;stroke-dasharray:5.0,5.0;" x1="1267.5" x2="1267.5" y1="81.2969" y2="924.2188"/><line style="stroke:#181818;stroke-width:0.5;stroke-dasharray:5.0,5.0;" x1="1546.5" x2="1546.5" y1="81.2969" y2="924.2188"/><line style="stroke:#181818;stroke-width:0.5;stroke-dasharray:5.0,5.0;" x1="2093.5" x2="2093.5" y1="81.2969" y2="924.2188"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="32" x="5" y="77.9951">User</text><ellipse cx="24" cy="13.5" fill="#E2E2F0" rx="8" ry="8" style="stroke:#181818;stroke-width:0.5;"/><path d="M24,21.5 L24,48.5 M11,29.5 L37,29.5 M24,48.5 L11,63.5 M24,48.5 L37,63.5 " fill="none" style="stroke:#181818;stroke-width:0.5;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="32" x="5" y="936.2139">User</text><ellipse cx="24" cy="948.0156" fill="#E2E2F0" rx="8" ry="8" style="stroke:#181818;stroke-width:0.5;"/><path d="M24,956.0156 L24,983.0156 M11,964.0156 L37,964.0156 M24,983.0156 L11,998.0156 M24,983.0156 L37,998.0156 " fill="none" style="stroke:#181818;stroke-width:0.5;"/><rect fill="#E2E2F0" height="30.2969" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="175" x="417.5" y="50"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="161" x="424.5" y="69.9951">LSTRestakingController</text><rect fill="#E2E2F0" height="30.2969" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="175" x="417.5" y="923.2188"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="161" x="424.5" y="943.2139">LSTRestakingController</text><rect fill="#E2E2F0" height="30.2969" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="185" x="1175.5" y="50"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="171" x="1182.5" y="69.9951">BaseRestakingController</text><rect fill="#E2E2F0" height="30.2969" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="185" x="1175.5" y="923.2188"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="171" x="1182.5" y="943.2139">BaseRestakingController</text><rect fill="#E2E2F0" height="30.2969" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="129" x="1482.5" y="50"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="115" x="1489.5" y="69.9951">ImuachainGateway</text><rect fill="#E2E2F0" height="30.2969" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="129" x="1482.5" y="923.2188"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="115" x="1489.5" y="943.2139">ImuachainGateway</text><rect fill="#E2E2F0" height="30.2969" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="151" x="2018.5" y="50"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="137" x="2025.5" y="69.9951">ClientChainGateway</text><rect fill="#E2E2F0" height="30.2969" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="151" x="2018.5" y="923.2188"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="137" x="2025.5" y="943.2139">ClientChainGateway</text><polygon fill="#181818" points="493,108.4297,503,112.4297,493,116.4297,497,112.4297" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="24" x2="499" y1="112.4297" y2="112.4297"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="258" x="31" y="107.3638">deposit(address token, uint256 amount)</text><polygon fill="#181818" points="493,137.5625,503,141.5625,493,145.5625,497,141.5625" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="24" x2="499" y1="141.5625" y2="141.5625"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="457" x="31" y="136.4966">withdrawPrincipleFromImuachain(address token, uint256 principleAmount)</text><polygon fill="#181818" points="493,166.6953,503,170.6953,493,174.6953,497,170.6953" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="24" x2="499" y1="170.6953" y2="170.6953"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="440" x="31" y="165.6294">withdrawPrincipleFromImuachain(address token, uint256 rewardAmount)</text><polygon fill="#181818" points="1256,195.8281,1266,199.8281,1256,203.8281,1260,199.8281" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="505" x2="1262" y1="199.8281" y2="199.8281"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="489" x="512" y="194.7622">_processRequest(token, msg.sender, amount, Action.REQUEST_DEPOSIT, "")</text><polygon fill="#181818" points="1256,224.9609,1266,228.9609,1256,232.9609,1260,228.9609" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="505" x2="1262" y1="228.9609" y2="228.9609"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="739" x="512" y="223.895">_processRequest(token, msg.sender, principleAmount, Action.REQUEST_WITHDRAW_PRINCIPLE_FROM_IMUACHAIN, "")</text><polygon fill="#181818" points="1256,254.0938,1266,258.0938,1256,262.0938,1260,258.0938" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="505" x2="1262" y1="258.0938" y2="258.0938"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="719" x="512" y="253.0278">_processRequest(token, msg.sender, rewardAmount, Action.REQUEST_WITHDRAW_REWARD_FROM_IMUACHAIN, "")</text><polygon fill="#181818" points="1535,283.2266,1545,287.2266,1535,291.2266,1539,287.2266" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="1268" x2="1541" y1="287.2266" y2="287.2266"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="255" x="1275" y="282.1606">_sendMsgToImuachain(action, actionArgs)</text><line style="stroke:#181818;stroke-width:1.0;" x1="1547" x2="1589" y1="316.3594" y2="316.3594"/><line style="stroke:#181818;stroke-width:1.0;" x1="1589" x2="1589" y1="316.3594" y2="329.3594"/><line style="stroke:#181818;stroke-width:1.0;" x1="1548" x2="1589" y1="329.3594" y2="329.3594"/><polygon fill="#181818" points="1558,325.3594,1548,329.3594,1558,333.3594,1554,329.3594" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="364" x="1554" y="311.2935">_lzReceive(Origin calldata _origin, bytes calldata payload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="1547" x2="1589" y1="358.4922" y2="358.4922"/><line style="stroke:#181818;stroke-width:1.0;" x1="1589" x2="1589" y1="358.4922" y2="371.4922"/><line style="stroke:#181818;stroke-width:1.0;" x1="1548" x2="1589" y1="371.4922" y2="371.4922"/><polygon fill="#181818" points="1558,367.4922,1548,371.4922,1558,375.4922,1554,371.4922" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="472" x="1554" y="353.4263">requestDeposit(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="1547" x2="1589" y1="400.625" y2="400.625"/><line style="stroke:#181818;stroke-width:1.0;" x1="1589" x2="1589" y1="400.625" y2="413.625"/><line style="stroke:#181818;stroke-width:1.0;" x1="1548" x2="1589" y1="413.625" y2="413.625"/><polygon fill="#181818" points="1558,409.625,1548,413.625,1558,417.625,1554,413.625" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="533" x="1554" y="395.5591">requestWithdrawPrinciple(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="1547" x2="1589" y1="442.7578" y2="442.7578"/><line style="stroke:#181818;stroke-width:1.0;" x1="1589" x2="1589" y1="442.7578" y2="455.7578"/><line style="stroke:#181818;stroke-width:1.0;" x1="1548" x2="1589" y1="455.7578" y2="455.7578"/><polygon fill="#181818" points="1558,451.7578,1548,455.7578,1558,459.7578,1554,455.7578" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="526" x="1554" y="437.6919">requestWithdrawReward(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="1547" x2="1589" y1="484.8906" y2="484.8906"/><line style="stroke:#181818;stroke-width:1.0;" x1="1589" x2="1589" y1="484.8906" y2="497.8906"/><line style="stroke:#181818;stroke-width:1.0;" x1="1548" x2="1589" y1="497.8906" y2="497.8906"/><polygon fill="#181818" points="1558,493.8906,1548,497.8906,1558,501.8906,1554,497.8906" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="496" x="1554" y="479.8247">requestDelegateTo(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="1547" x2="1589" y1="527.0234" y2="527.0234"/><line style="stroke:#181818;stroke-width:1.0;" x1="1589" x2="1589" y1="527.0234" y2="540.0234"/><line style="stroke:#181818;stroke-width:1.0;" x1="1548" x2="1589" y1="540.0234" y2="540.0234"/><polygon fill="#181818" points="1558,536.0234,1548,540.0234,1558,544.0234,1554,540.0234" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="530" x="1554" y="521.9575">requestUndelegateFrom(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><polygon fill="#181818" points="2082,565.1563,2092,569.1563,2082,573.1563,2086,569.1563" style="stroke:#181818;stroke-width:1.0;"/><line style="stroke:#181818;stroke-width:1.0;" x1="1547" x2="2088" y1="569.1563" y2="569.1563"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="448" x="1554" y="564.0903">_sendInterchainMsg(srcChainId, Action act, bytes memory actionArgs)</text><line style="stroke:#181818;stroke-width:1.0;" x1="2094" x2="2136" y1="598.2891" y2="598.2891"/><line style="stroke:#181818;stroke-width:1.0;" x1="2136" x2="2136" y1="598.2891" y2="611.2891"/><line style="stroke:#181818;stroke-width:1.0;" x1="2095" x2="2136" y1="611.2891" y2="611.2891"/><polygon fill="#181818" points="2105,607.2891,2095,611.2891,2105,615.2891,2101,611.2891" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="364" x="2101" y="593.2231">_lzReceive(Origin calldata _origin, bytes calldata payload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="2094" x2="2136" y1="640.4219" y2="640.4219"/><line style="stroke:#181818;stroke-width:1.0;" x1="2136" x2="2136" y1="640.4219" y2="653.4219"/><line style="stroke:#181818;stroke-width:1.0;" x1="2095" x2="2136" y1="653.4219" y2="653.4219"/><polygon fill="#181818" points="2105,649.4219,2095,653.4219,2105,657.4219,2101,653.4219" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="268" x="2101" y="635.356">nextNonce(uint32 srcEid, bytes32 sender)</text><line style="stroke:#181818;stroke-width:1.0;" x1="2094" x2="2136" y1="682.5547" y2="682.5547"/><line style="stroke:#181818;stroke-width:1.0;" x1="2136" x2="2136" y1="682.5547" y2="695.5547"/><line style="stroke:#181818;stroke-width:1.0;" x1="2095" x2="2136" y1="695.5547" y2="695.5547"/><polygon fill="#181818" points="2105,691.5547,2095,695.5547,2105,699.5547,2101,695.5547" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="448" x="2101" y="677.4888">_verifyAndUpdateNonce(uint32 srcEid, bytes32 sender, uint64 nonce)</text><line style="stroke:#181818;stroke-width:1.0;" x1="2094" x2="2136" y1="724.6875" y2="724.6875"/><line style="stroke:#181818;stroke-width:1.0;" x1="2136" x2="2136" y1="724.6875" y2="737.6875"/><line style="stroke:#181818;stroke-width:1.0;" x1="2095" x2="2136" y1="737.6875" y2="737.6875"/><polygon fill="#181818" points="2105,733.6875,2095,737.6875,2105,741.6875,2101,737.6875" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="606" x="2101" y="719.6216">afterReceiveDepositResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="2094" x2="2136" y1="766.8203" y2="766.8203"/><line style="stroke:#181818;stroke-width:1.0;" x1="2136" x2="2136" y1="766.8203" y2="779.8203"/><line style="stroke:#181818;stroke-width:1.0;" x1="2095" x2="2136" y1="779.8203" y2="779.8203"/><polygon fill="#181818" points="2105,775.8203,2095,779.8203,2105,783.8203,2101,779.8203" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="667" x="2101" y="761.7544">afterReceiveWithdrawPrincipleResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="2094" x2="2136" y1="808.9531" y2="808.9531"/><line style="stroke:#181818;stroke-width:1.0;" x1="2136" x2="2136" y1="808.9531" y2="821.9531"/><line style="stroke:#181818;stroke-width:1.0;" x1="2095" x2="2136" y1="821.9531" y2="821.9531"/><polygon fill="#181818" points="2105,817.9531,2095,821.9531,2105,825.9531,2101,821.9531" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="660" x="2101" y="803.8872">afterReceiveWithdrawRewardResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="2094" x2="2136" y1="851.0859" y2="851.0859"/><line style="stroke:#181818;stroke-width:1.0;" x1="2136" x2="2136" y1="851.0859" y2="864.0859"/><line style="stroke:#181818;stroke-width:1.0;" x1="2095" x2="2136" y1="864.0859" y2="864.0859"/><polygon fill="#181818" points="2105,860.0859,2095,864.0859,2105,868.0859,2101,864.0859" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="615" x="2101" y="846.02">afterReceiveDelegateResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><line style="stroke:#181818;stroke-width:1.0;" x1="2094" x2="2136" y1="893.2188" y2="893.2188"/><line style="stroke:#181818;stroke-width:1.0;" x1="2136" x2="2136" y1="893.2188" y2="906.2188"/><line style="stroke:#181818;stroke-width:1.0;" x1="2095" x2="2136" y1="906.2188" y2="906.2188"/><polygon fill="#181818" points="2105,902.2188,2095,906.2188,2105,910.2188,2101,906.2188" style="stroke:#181818;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="631" x="2101" y="888.1528">afterReceiveUndelegateResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><!--SRC=[pPHFJzim6CRl_HGMfrJAEB2DmmwJGXgsI40oDrLtYupz4YmIExFTbVBfPoSfj3JLMW65IwguplDxzBn_23LIeOa6DHYu9_hq7LsCCmpQa3ikYb0AesHPWlg66DHIS-CHnXHeZOoy0-6Z6HVcvEif8fMS2JFyhyoScrkco3nLN51Ubt2kP1NTIoeLRDEkbtU3FI4OvaIn7GYgkRYK7_JhEk8PqT2Bp6ibgLN6y6Tc__SwGAMBOms2WVAxY7mKKCEbECRHhqaqpl9Hb2RZEFFHqT7mZSZF0jomC8spdoCSJFCKnrTXd5v4-JbEBlFeTn8cE7fBQwl9R_U5esc0Hpsc1blOpbPNyn_4MCR2kd7CIrrayh4R6fojJFCRg49RnVNFUeNA1mmK-5_m4iKBBX0bPSc88IYNpO6FhXS6zDDvJHQb96ouWxfgmnfry-PQzFC9qeg6juIBcBLTU_e5bGzNKb1u1TRq-L0UbDfEwK6G8oYXi6UPF0XkOdky1ReTz19a3pCiEGZJAAstU2mCAEhERRDxgxHsl5oxBqaLL58jrWTWKtWhxWM3i3j5mBrfuba9CEAiatRlpIxPHpAdKkXP1R6ujek7xQ3-M2hXxkx38ZUs75rCtL3RXLLR0-2j5Q5hchHDQ2CvrNsKxf_WgeMDMVy8PjeLy9vEbflXFJqyhOjNSV4F]--></g></svg>
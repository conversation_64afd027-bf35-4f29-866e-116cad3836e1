<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="361px" height="423px" viewBox="-0.5 -0.5 361 423" content="&lt;mxfile&gt;&lt;diagram id=&quot;Z_ywo95Vat48J5i4v36a&quot; name=&quot;Page-1&quot;&gt;5VbBjtsgEP2aHLOyje2kxybZtodWqpRD2yOxiY0WGwtwnOzXdzDYDsGRIjVVD70kzAMG5r2ZwQu0rc6fBW7KbzwnbBEF+XmBdosoWqMQfjVwMUCYRGuDFILmFpuAPX0nFgws2tKcSGeh4pwp2rhgxuuaZMrBsBC8c5cdOXNPbXBBPGCfYeajP2iuShtXtJrwL4QW5XBymH4wMxUeFttIZIlz3l1B6HWBtoJzZUbVeUuYJm/gxez7dGd2vJggtXpkQ2o2nDBrbWz2XuoyBEtyiN2aNa/hb1OqioEVwhDOEZefYAQvyWD+up7badWD0bpYyxyjfd+9uYUkb0VmV9nbKSwKYlehkTVIN8IrAqfAEkEYVvTkesdW92JcN1EDA8vOPFMrj6lMcCmXWYlp3edaVbU1zeBUXs+S+BUfoA4c9jCjRQ3jDIImAoATEQp8sI92oqJ5rn1sBJH0HR96f5q/htNa9eEkm0WyGxnVDsh5rgrs5in3rrlO51m0jpbBS4BWlgFbtFEYG/thpq377/rmk+/Rz7CFH48SxL2VZrzVQ2pFnlpS4TeS9+eAewmDHk9x1QB5heoZ1PUITAPMNHAQMOqnBnx+k+862vTMdFjkc94YlqUPm/V+7jAGjU0nQVdSRfYN7suhg97qZtPdDPCq6q7SEYpdlaPE2N3U51BqE6i87nFx8Oc1hjzVTrgFkjym3NJruFCY/VPeUOLyNrxUV7SF0Qxt6yewFvudiQsCCMMXaCq3tECQ6u91IdwqLs2DrV1LJfgb2XLGxfR8HCljN9AzJFi5EqA08jRYz0iAniBB4kvAKOnb3PQ+wPuHM90e/hdBYleQJZopivg5ioA5fTSZ92L69ESvvwE=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 180 255 L 180 126.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 180 121.12 L 183.5 128.12 L 180 126.37 L 176.5 128.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 191px; margin-left: 181px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                cross-chain communication
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="181" y="194" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    cross-chain communication
                </text>
            </switch>
        </g>
        <ellipse cx="180" cy="325" rx="180" ry="70" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 325px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                staked assets =&gt; shares
                                <br/>
                                shares =&gt; staked assets + rewards
                                <br/>
                                slash
                                <br/>
                                reward
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="329" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    staked assets =&gt; shares...
                </text>
            </switch>
        </g>
        <ellipse cx="180" cy="80" rx="60" ry="40" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 80px; margin-left: 121px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                vault
                                <br/>
                                cross-chain portal
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="84" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    vault...
                </text>
            </switch>
        </g>
        <rect x="140" y="392" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 407px; margin-left: 180px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                core layer
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="411" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    core layer
                </text>
            </switch>
        </g>
        <rect x="110" y="0" width="140" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 15px; margin-left: 180px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                client chain contracts
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="19" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    client chain contracts
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
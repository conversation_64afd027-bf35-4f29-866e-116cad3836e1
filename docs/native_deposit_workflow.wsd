### all functions

@startuml
actor User
participant NativeStakingController
participant ETHPOS
participant <PERSON>lientChainLzReceiver
participant ImuaCapsule
participant ClientChainL0Endpoint
participant ImuachainL0Endpoint
participant ImuachainGateway
participant DepositPrecompile
participant DepositNativeModule

User -> NativeStakingController: 1.1:stake(pubkey, signature, depositDataRoot)
activate NativeStakingController
NativeStakingController -> ETHPOS: 1.2:deposit()
activate ETHPOS
ETHPOS -> NativeStakingController: 1.3:DepositSuc<PERSON>
deactivate ETHPOS
deactivate NativeStakingController

User -> NativeStakingController: 2.1:verifyAndDepositNativeStake(validatorContainer, proof)
activate NativeStakingController
NativeStakingController -> ImuaCapsule: 2.2:verifyDepositProof(validatorContainer, proof)
activate ImuaCapsule
ImuaCapsule -> NativeStakingController: 2.3:return (isValidValidatorContainer)
deactivate ImuaCapsule
NativeStakingController -> ClientChainL0Endpoint: 2.4:send(request)
activate ClientChainL0Endpoint
ClientChainL0Endpoint -> NativeStakingController: 2.5:emit (requestSent)
deactivate ClientChainL0Endpoint
deactivate NativeStakingController
ClientChainL0Endpoint -> ImuachainL0Endpoint: 3.1:lzReceive(request)
activate ImuachainL0Endpoint
ImuachainL0Endpoint -> ImuachainGateway: 3.2:lzReceive(request)
activate ImuachainGateway
ImuachainGateway -> ImuachainGateway: 3.3:handleNSTTransfer(payload)
ImuachainGateway -> DepositPrecompile: 3.4:depositNST(payload)
activate DepositPrecompile
DepositPrecompile -> DepositNativeModule: 3.5:depositTo(payload)
activate DepositNativeModule
DepositNativeModule -> DepositPrecompile: 3.6:return (result, balance)
deactivate DepositNativeModule
DepositPrecompile -> ImuachainGateway: 3.7:return (result, balance)
deactivate DepositPrecompile
ImuachainGateway -> ImuachainGateway: 3.9:emit (NSTTransfer)
deactivate ImuachainGateway
deactivate ImuachainL0Endpoint

@enduml


@startuml
title NativeRestakingController: verifyAndDepositNativeStake() function

start

:Get the capsule associated with the message sender;
if (capsule == address(0)) then (yes)
    :Revert with CapsuleDoesNotExist error;
    stop
endif

:Call capsule.verifyDepositProof(validatorContainer, proof);
fork
    :Check if caller is gateway;
    if (msg.sender != gateway) then (no)
        :Revert with "ImuaCapsule: only client chain gateway could call this function";
        stop
    endif

    :Get validator pubkey and withdrawal credentials from validatorContainer;
    :Get Validator struct for pubkey from _capsuleValidators;

    if (validator.status != UNREGISTERED) then (yes)
        :Revert with DoubleDepositedValidator error;
        stop
    endif

    if (_isStaleProof(validator, proof.beaconBlockTimestamp)) then (yes)
        :Revert with StaleValidatorContainer error;
        stop
    endif

    if (!validatorContainer.verifyValidatorContainerBasic()) then (no)
        :Revert with InvalidValidatorContainer error;
        stop
    endif

    if (!_isActivatedAtEpoch(validatorContainer, proof.beaconBlockTimestamp)) then (no)
        :Revert with InvalidValidatorContainer error;
        stop
    endif

    if (withdrawalCredentials != capsuleWithdrawalCredentials()) then (yes)
        :Revert with InvalidValidatorContainer error;
        stop
    endif

    :Verify validator container using _verifyValidatorContainer();
    :Update Validator struct with new status, index, and balance;
    :Store validator pubkey in _capsuleValidatorsByIndex;
fork again

:Calculate the depositValue using validatorContainer.getEffectiveBalance();
:Store the request details in registeredRequests and registeredRequestActions;

:Encode the request action arguments;
:Send the request action to Imuachain using _sendMsgToImuachain();

stop

@enduml
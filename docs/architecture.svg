<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="1351px" preserveAspectRatio="none" style="width:5846px;height:1351px;background:#FFFFFF;" version="1.1" viewBox="0 0 5846 1351" width="5846px" zoomAndPan="magnify"><defs/><g><!--class IImuachainGateway--><g id="elem_IImuachainGateway"><rect codeLine="1" fill="#F1F1F1" height="48" id="IImuachainGateway" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="150" x="105" y="223"/><ellipse cx="120" cy="239" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M115.9219,234.7656 L115.9219,232.6094 L123.3125,232.6094 L123.3125,234.7656 L120.8438,234.7656 L120.8438,242.8438 L123.3125,242.8438 L123.3125,245 L115.9219,245 L115.9219,242.8438 L118.3906,242.8438 L118.3906,234.7656 L115.9219,234.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="118" x="134" y="243.8467">IImuachainGateway</text><line style="stroke:#181818;stroke-width:0.5;" x1="106" x2="254" y1="255" y2="255"/><line style="stroke:#181818;stroke-width:0.5;" x1="106" x2="254" y1="263" y2="263"/></g><!--class ILSTRestakingController--><g id="elem_ILSTRestakingController"><rect codeLine="2" fill="#F1F1F1" height="48" id="ILSTRestakingController" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="196" x="1559" y="445.08"/><ellipse cx="1574" cy="461.08" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M1569.9219,456.8456 L1569.9219,454.6894 L1577.3125,454.6894 L1577.3125,456.8456 L1574.8438,456.8456 L1574.8438,464.9238 L1577.3125,464.9238 L1577.3125,467.08 L1569.9219,467.08 L1569.9219,464.9238 L1572.3906,464.9238 L1572.3906,456.8456 L1569.9219,456.8456 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="164" x="1588" y="465.9267">ILSTRestakingController</text><line style="stroke:#181818;stroke-width:0.5;" x1="1560" x2="1754" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="1560" x2="1754" y1="485.08" y2="485.08"/></g><!--class IVault--><g id="elem_IVault"><rect codeLine="3" fill="#F1F1F1" height="48" id="IVault" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="69" x="2023.5" y="7"/><ellipse cx="2038.5" cy="23" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2034.4219,18.7656 L2034.4219,16.6094 L2041.8125,16.6094 L2041.8125,18.7656 L2039.3438,18.7656 L2039.3438,26.8438 L2041.8125,26.8438 L2041.8125,29 L2034.4219,29 L2034.4219,26.8438 L2036.8906,26.8438 L2036.8906,18.7656 L2034.4219,18.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="37" x="2052.5" y="27.8467">IVault</text><line style="stroke:#181818;stroke-width:0.5;" x1="2024.5" x2="2091.5" y1="39" y2="39"/><line style="stroke:#181818;stroke-width:0.5;" x1="2024.5" x2="2091.5" y1="47" y2="47"/></g><!--class IImuaCapsule--><g id="elem_IImuaCapsule"><rect codeLine="4" fill="#F1F1F1" height="48" id="IImuaCapsule" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="116" x="5159" y="445.08"/><ellipse cx="5174" cy="461.08" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M5169.9219,456.8456 L5169.9219,454.6894 L5177.3125,454.6894 L5177.3125,456.8456 L5174.8438,456.8456 L5174.8438,464.9238 L5177.3125,464.9238 L5177.3125,467.08 L5169.9219,467.08 L5169.9219,464.9238 L5172.3906,464.9238 L5172.3906,456.8456 L5169.9219,456.8456 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="84" x="5188" y="465.9267">IImuaCapsule</text><line style="stroke:#181818;stroke-width:0.5;" x1="5160" x2="5274" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="5160" x2="5274" y1="485.08" y2="485.08"/></g><!--class IBaseRestakingController--><g id="elem_IBaseRestakingController"><rect codeLine="5" fill="#F1F1F1" height="48" id="IBaseRestakingController" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="206" x="1864" y="223"/><ellipse cx="1879" cy="239" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M1874.9219,234.7656 L1874.9219,232.6094 L1882.3125,232.6094 L1882.3125,234.7656 L1879.8438,234.7656 L1879.8438,242.8438 L1882.3125,242.8438 L1882.3125,245 L1874.9219,245 L1874.9219,242.8438 L1877.3906,242.8438 L1877.3906,234.7656 L1874.9219,234.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="174" x="1893" y="243.8467">IBaseRestakingController</text><line style="stroke:#181818;stroke-width:0.5;" x1="1865" x2="2069" y1="255" y2="255"/><line style="stroke:#181818;stroke-width:0.5;" x1="1865" x2="2069" y1="263" y2="263"/></g><!--class ILayerZeroReceiver--><g id="elem_ILayerZeroReceiver"><rect codeLine="6" fill="#F1F1F1" height="48" id="ILayerZeroReceiver" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="164" x="2127" y="7"/><ellipse cx="2142" cy="23" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2137.9219,18.7656 L2137.9219,16.6094 L2145.3125,16.6094 L2145.3125,18.7656 L2142.8438,18.7656 L2142.8438,26.8438 L2145.3125,26.8438 L2145.3125,29 L2137.9219,29 L2137.9219,26.8438 L2140.3906,26.8438 L2140.3906,18.7656 L2137.9219,18.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="132" x="2156" y="27.8467">ILayerZeroReceiver</text><line style="stroke:#181818;stroke-width:0.5;" x1="2128" x2="2290" y1="39" y2="39"/><line style="stroke:#181818;stroke-width:0.5;" x1="2128" x2="2290" y1="47" y2="47"/></g><!--class IClientChains--><g id="elem_IClientChains"><rect codeLine="7" fill="#F1F1F1" height="48" id="IClientChains" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="121" x="2326.5" y="7"/><ellipse cx="2341.5" cy="23" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2337.4219,18.7656 L2337.4219,16.6094 L2344.8125,16.6094 L2344.8125,18.7656 L2342.3438,18.7656 L2342.3438,26.8438 L2344.8125,26.8438 L2344.8125,29 L2337.4219,29 L2337.4219,26.8438 L2339.8906,26.8438 L2339.8906,18.7656 L2337.4219,18.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="89" x="2355.5" y="27.8467">IClientChains</text><line style="stroke:#181818;stroke-width:0.5;" x1="2327.5" x2="2446.5" y1="39" y2="39"/><line style="stroke:#181818;stroke-width:0.5;" x1="2327.5" x2="2446.5" y1="47" y2="47"/></g><!--class IOAppReceiver--><g id="elem_IOAppReceiver"><rect codeLine="8" fill="#F1F1F1" height="48" id="IOAppReceiver" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="133" x="3256.5" y="445.08"/><ellipse cx="3271.5" cy="461.08" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3267.4219,456.8456 L3267.4219,454.6894 L3274.8125,454.6894 L3274.8125,456.8456 L3272.3438,456.8456 L3272.3438,464.9238 L3274.8125,464.9238 L3274.8125,467.08 L3267.4219,467.08 L3267.4219,464.9238 L3269.8906,464.9238 L3269.8906,456.8456 L3267.4219,456.8456 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="101" x="3285.5" y="465.9267">IOAppReceiver</text><line style="stroke:#181818;stroke-width:0.5;" x1="3257.5" x2="3388.5" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="3257.5" x2="3388.5" y1="485.08" y2="485.08"/></g><!--class IOAppCore--><g id="elem_IOAppCore"><rect codeLine="9" fill="#F1F1F1" height="48" id="IOAppCore" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="106" x="3424" y="445.08"/><ellipse cx="3439" cy="461.08" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3434.9219,456.8456 L3434.9219,454.6894 L3442.3125,454.6894 L3442.3125,456.8456 L3439.8438,456.8456 L3439.8438,464.9238 L3442.3125,464.9238 L3442.3125,467.08 L3434.9219,467.08 L3434.9219,464.9238 L3437.3906,464.9238 L3437.3906,456.8456 L3434.9219,456.8456 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="74" x="3453" y="465.9267">IOAppCore</text><line style="stroke:#181818;stroke-width:0.5;" x1="3425" x2="3529" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="3425" x2="3529" y1="485.08" y2="485.08"/></g><!--class INativeRestakingController--><g id="elem_INativeRestakingController"><rect codeLine="10" fill="#F1F1F1" height="48" id="INativeRestakingController" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="213" x="1860.5" y="445.08"/><ellipse cx="1875.5" cy="461.08" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M1871.4219,456.8456 L1871.4219,454.6894 L1878.8125,454.6894 L1878.8125,456.8456 L1876.3438,456.8456 L1876.3438,464.9238 L1878.8125,464.9238 L1878.8125,467.08 L1871.4219,467.08 L1871.4219,464.9238 L1873.8906,464.9238 L1873.8906,456.8456 L1871.4219,456.8456 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="181" x="1889.5" y="465.9267">INativeRestakingController</text><line style="stroke:#181818;stroke-width:0.5;" x1="1861.5" x2="2072.5" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="1861.5" x2="2072.5" y1="485.08" y2="485.08"/></g><!--class IBeaconChainOracle--><g id="elem_IBeaconChainOracle"><rect codeLine="11" fill="#F1F1F1" height="48" id="IBeaconChainOracle" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="171" x="1972.5" y="115"/><ellipse cx="1987.5" cy="131" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M1983.4219,126.7656 L1983.4219,124.6094 L1990.8125,124.6094 L1990.8125,126.7656 L1988.3438,126.7656 L1988.3438,134.8438 L1990.8125,134.8438 L1990.8125,137 L1983.4219,137 L1983.4219,134.8438 L1985.8906,134.8438 L1985.8906,126.7656 L1983.4219,126.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="139" x="2001.5" y="135.8467">IBeaconChainOracle</text><line style="stroke:#181818;stroke-width:0.5;" x1="1973.5" x2="2142.5" y1="147" y2="147"/><line style="stroke:#181818;stroke-width:0.5;" x1="1973.5" x2="2142.5" y1="155" y2="155"/></g><!--class IETHPOSDeposit--><g id="elem_IETHPOSDeposit"><rect codeLine="12" fill="#F1F1F1" height="48" id="IETHPOSDeposit" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="146" x="2179" y="115"/><ellipse cx="2194" cy="131" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2189.9219,126.7656 L2189.9219,124.6094 L2197.3125,124.6094 L2197.3125,126.7656 L2194.8438,126.7656 L2194.8438,134.8438 L2197.3125,134.8438 L2197.3125,137 L2189.9219,137 L2189.9219,134.8438 L2192.3906,134.8438 L2192.3906,126.7656 L2189.9219,126.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="114" x="2208" y="135.8467">IETHPOSDeposit</text><line style="stroke:#181818;stroke-width:0.5;" x1="2180" x2="2324" y1="147" y2="147"/><line style="stroke:#181818;stroke-width:0.5;" x1="2180" x2="2324" y1="155" y2="155"/></g><!--class ITokenWhitelister--><g id="elem_ITokenWhitelister"><rect codeLine="13" fill="#F1F1F1" height="48" id="ITokenWhitelister" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="151" x="1302.5" y="445.08"/><ellipse cx="1317.5" cy="461.08" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M1313.4219,456.8456 L1313.4219,454.6894 L1320.8125,454.6894 L1320.8125,456.8456 L1318.3438,456.8456 L1318.3438,464.9238 L1320.8125,464.9238 L1320.8125,467.08 L1313.4219,467.08 L1313.4219,464.9238 L1315.8906,464.9238 L1315.8906,456.8456 L1313.4219,456.8456 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="119" x="1331.5" y="465.9267">ITokenWhitelister</text><line style="stroke:#181818;stroke-width:0.5;" x1="1303.5" x2="1452.5" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="1303.5" x2="1452.5" y1="485.08" y2="485.08"/></g><!--class IValidatorRegistry--><g id="elem_IValidatorRegistry"><rect codeLine="14" fill="#F1F1F1" height="48" id="IValidatorRegistry" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="152" x="905" y="445.08"/><ellipse cx="920" cy="461.08" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M915.9219,456.8456 L915.9219,454.6894 L923.3125,454.6894 L923.3125,456.8456 L920.8438,456.8456 L920.8438,464.9238 L923.3125,464.9238 L923.3125,467.08 L915.9219,467.08 L915.9219,464.9238 L918.3906,464.9238 L918.3906,456.8456 L915.9219,456.8456 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="120" x="934" y="465.9267">IValidatorRegistry</text><line style="stroke:#181818;stroke-width:0.5;" x1="906" x2="1056" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="906" x2="1056" y1="485.08" y2="485.08"/></g><!--class PausableUpgradeable--><g id="elem_PausableUpgradeable"><rect codeLine="16" fill="#F1F1F1" height="48" id="PausableUpgradeable" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="183" x="2578.5" y="223"/><ellipse cx="2593.5" cy="239" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2593.6094,234.3438 L2592.4531,239.4219 L2594.7813,239.4219 L2593.6094,234.3438 Z M2592.125,232.1094 L2595.1094,232.1094 L2598.4688,244.5 L2596.0156,244.5 L2595.25,241.4375 L2591.9688,241.4375 L2591.2188,244.5 L2588.7813,244.5 L2592.125,232.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="151" x="2607.5" y="243.8467">PausableUpgradeable</text><line style="stroke:#181818;stroke-width:0.5;" x1="2579.5" x2="2760.5" y1="255" y2="255"/><line style="stroke:#181818;stroke-width:0.5;" x1="2579.5" x2="2760.5" y1="263" y2="263"/></g><!--class OwnableUpgradeable--><g id="elem_OwnableUpgradeable"><rect codeLine="17" fill="#F1F1F1" height="48" id="OwnableUpgradeable" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="180" x="598" y="223"/><ellipse cx="613" cy="239" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M613.1094,234.3438 L611.9531,239.4219 L614.2813,239.4219 L613.1094,234.3438 Z M611.625,232.1094 L614.6094,232.1094 L617.9688,244.5 L615.5156,244.5 L614.75,241.4375 L611.4688,241.4375 L610.7188,244.5 L608.2813,244.5 L611.625,232.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="148" x="627" y="243.8467">OwnableUpgradeable</text><line style="stroke:#181818;stroke-width:0.5;" x1="599" x2="777" y1="255" y2="255"/><line style="stroke:#181818;stroke-width:0.5;" x1="599" x2="777" y1="263" y2="263"/></g><!--class Initializable--><g id="elem_Initializable"><rect codeLine="18" fill="#F1F1F1" height="48" id="Initializable" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="106" x="3116" y="445.08"/><ellipse cx="3131" cy="461.08" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3131.1094,456.4238 L3129.9531,461.5019 L3132.2813,461.5019 L3131.1094,456.4238 Z M3129.625,454.1894 L3132.6094,454.1894 L3135.9688,466.58 L3133.5156,466.58 L3132.75,463.5175 L3129.4688,463.5175 L3128.7188,466.58 L3126.2813,466.58 L3129.625,454.1894 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="74" x="3145" y="465.9267">Initializable</text><line style="stroke:#181818;stroke-width:0.5;" x1="3117" x2="3221" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="3117" x2="3221" y1="485.08" y2="485.08"/></g><!--class OAppUpgradeable--><g id="elem_OAppUpgradeable"><rect codeLine="19" fill="#F1F1F1" height="48" id="OAppUpgradeable" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="158" x="2360" y="115"/><ellipse cx="2375" cy="131" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2375.1094,126.3438 L2373.9531,131.4219 L2376.2813,131.4219 L2375.1094,126.3438 Z M2373.625,124.1094 L2376.6094,124.1094 L2379.9688,136.5 L2377.5156,136.5 L2376.75,133.4375 L2373.4688,133.4375 L2372.7188,136.5 L2370.2813,136.5 L2373.625,124.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="126" x="2389" y="135.8467">OAppUpgradeable</text><line style="stroke:#181818;stroke-width:0.5;" x1="2361" x2="2517" y1="147" y2="147"/><line style="stroke:#181818;stroke-width:0.5;" x1="2361" x2="2517" y1="155" y2="155"/></g><!--class OAppSenderUpgradeable--><g id="elem_OAppSenderUpgradeable"><rect codeLine="20" fill="#F1F1F1" height="48" id="OAppSenderUpgradeable" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="208" x="2335" y="223"/><ellipse cx="2350" cy="239" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2350.1094,234.3438 L2348.9531,239.4219 L2351.2813,239.4219 L2350.1094,234.3438 Z M2348.625,232.1094 L2351.6094,232.1094 L2354.9688,244.5 L2352.5156,244.5 L2351.75,241.4375 L2348.4688,241.4375 L2347.7188,244.5 L2345.2813,244.5 L2348.625,232.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="176" x="2364" y="243.8467">OAppSenderUpgradeable</text><line style="stroke:#181818;stroke-width:0.5;" x1="2336" x2="2542" y1="255" y2="255"/><line style="stroke:#181818;stroke-width:0.5;" x1="2336" x2="2542" y1="263" y2="263"/></g><!--class OAppReceiverUpgradeable--><g id="elem_OAppReceiverUpgradeable"><rect codeLine="21" fill="#F1F1F1" height="48" id="OAppReceiverUpgradeable" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="218" x="3241" y="223"/><ellipse cx="3256" cy="239" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3256.1094,234.3438 L3254.9531,239.4219 L3257.2813,239.4219 L3256.1094,234.3438 Z M3254.625,232.1094 L3257.6094,232.1094 L3260.9688,244.5 L3258.5156,244.5 L3257.75,241.4375 L3254.4688,241.4375 L3253.7188,244.5 L3251.2813,244.5 L3254.625,232.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="186" x="3270" y="243.8467">OAppReceiverUpgradeable</text><line style="stroke:#181818;stroke-width:0.5;" x1="3242" x2="3458" y1="255" y2="255"/><line style="stroke:#181818;stroke-width:0.5;" x1="3242" x2="3458" y1="263" y2="263"/></g><!--class GatewayStorage--><g id="elem_GatewayStorage"><rect codeLine="22" fill="#F1F1F1" height="48" id="GatewayStorage" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="145" x="3156.5" y="7"/><ellipse cx="3171.5" cy="23" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3171.6094,18.3438 L3170.4531,23.4219 L3172.7813,23.4219 L3171.6094,18.3438 Z M3170.125,16.1094 L3173.1094,16.1094 L3176.4688,28.5 L3174.0156,28.5 L3173.25,25.4375 L3169.9688,25.4375 L3169.2188,28.5 L3166.7813,28.5 L3170.125,16.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="113" x="3185.5" y="27.8467">GatewayStorage</text><line style="stroke:#181818;stroke-width:0.5;" x1="3157.5" x2="3300.5" y1="39" y2="39"/><line style="stroke:#181818;stroke-width:0.5;" x1="3157.5" x2="3300.5" y1="47" y2="47"/></g><!--class BootstrapStorage--><g id="elem_BootstrapStorage"><rect codeLine="23" fill="#F1F1F1" height="48" id="BootstrapStorage" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="154" x="3152" y="115"/><ellipse cx="3167" cy="131" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3167.1094,126.3438 L3165.9531,131.4219 L3168.2813,131.4219 L3167.1094,126.3438 Z M3165.625,124.1094 L3168.6094,124.1094 L3171.9688,136.5 L3169.5156,136.5 L3168.75,133.4375 L3165.4688,133.4375 L3164.7188,136.5 L3162.2813,136.5 L3165.625,124.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="122" x="3181" y="135.8467">BootstrapStorage</text><line style="stroke:#181818;stroke-width:0.5;" x1="3153" x2="3305" y1="147" y2="147"/><line style="stroke:#181818;stroke-width:0.5;" x1="3153" x2="3305" y1="155" y2="155"/></g><!--class ClientChainGatewayStorage--><g id="elem_ClientChainGatewayStorage"><rect codeLine="24" fill="#F1F1F1" height="48" id="ClientChainGatewayStorage" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="223" x="3716.5" y="223"/><ellipse cx="3731.5" cy="239" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3731.6094,234.3438 L3730.4531,239.4219 L3732.7813,239.4219 L3731.6094,234.3438 Z M3730.125,232.1094 L3733.1094,232.1094 L3736.4688,244.5 L3734.0156,244.5 L3733.25,241.4375 L3729.9688,241.4375 L3729.2188,244.5 L3726.7813,244.5 L3730.125,232.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="191" x="3745.5" y="243.8467">ClientChainGatewayStorage</text><line style="stroke:#181818;stroke-width:0.5;" x1="3717.5" x2="3938.5" y1="255" y2="255"/><line style="stroke:#181818;stroke-width:0.5;" x1="3717.5" x2="3938.5" y1="263" y2="263"/></g><!--class ImuachainGatewayStorage--><g id="elem_ImuachainGatewayStorage"><rect codeLine="25" fill="#F1F1F1" height="48" id="ImuachainGatewayStorage" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="201" x="361.5" y="223"/><ellipse cx="376.5" cy="239" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M376.6094,234.3438 L375.4531,239.4219 L377.7813,239.4219 L376.6094,234.3438 Z M375.125,232.1094 L378.1094,232.1094 L381.4688,244.5 L379.0156,244.5 L378.25,241.4375 L374.9688,241.4375 L374.2188,244.5 L371.7813,244.5 L375.125,232.1094 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="169" x="390.5" y="243.8467">ImuachainGatewayStorage</text><line style="stroke:#181818;stroke-width:0.5;" x1="362.5" x2="561.5" y1="255" y2="255"/><line style="stroke:#181818;stroke-width:0.5;" x1="362.5" x2="561.5" y1="263" y2="263"/></g><!--class ImuaCapsuleStorage--><g id="elem_ImuaCapsuleStorage"><rect codeLine="26" fill="#F1F1F1" height="48" id="ImuaCapsuleStorage" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="167" x="4957.5" y="445.08"/><ellipse cx="4972.5" cy="461.08" fill="#A9DCDF" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M4972.6094,456.4238 L4971.4531,461.5019 L4973.7813,461.5019 L4972.6094,456.4238 Z M4971.125,454.1894 L4974.1094,454.1894 L4977.4688,466.58 L4975.0156,466.58 L4974.25,463.5175 L4970.9688,463.5175 L4970.2188,466.58 L4967.7813,466.58 L4971.125,454.1894 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="135" x="4986.5" y="465.9267">ImuaCapsuleStorage</text><line style="stroke:#181818;stroke-width:0.5;" x1="4958.5" x2="5123.5" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="4958.5" x2="5123.5" y1="485.08" y2="485.08"/></g><!--class BaseRestakingController--><g id="elem_BaseRestakingController"><rect codeLine="28" fill="#F1F1F1" height="129.4844" id="BaseRestakingController" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="760" x="2179" y="404.34"/><ellipse cx="2469.25" cy="420.34" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2472.2188,425.9806 Q2471.6406,426.2775 2471,426.4181 Q2470.3594,426.5744 2469.6563,426.5744 Q2467.1563,426.5744 2465.8281,424.9338 Q2464.5156,423.2775 2464.5156,420.1525 Q2464.5156,417.0275 2465.8281,415.3713 Q2467.1563,413.715 2469.6563,413.715 Q2470.3594,413.715 2471,413.8713 Q2471.6563,414.0275 2472.2188,414.3244 L2472.2188,417.0431 Q2471.5938,416.465 2471,416.1994 Q2470.4063,415.9181 2469.7813,415.9181 Q2468.4375,415.9181 2467.75,416.9963 Q2467.0625,418.0588 2467.0625,420.1525 Q2467.0625,422.2463 2467.75,423.3244 Q2468.4375,424.3869 2469.7813,424.3869 Q2470.4063,424.3869 2471,424.1213 Q2471.5938,423.84 2472.2188,423.2619 L2472.2188,425.9806 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="171" x="2489.75" y="425.1867">BaseRestakingController</text><line style="stroke:#181818;stroke-width:0.5;" x1="2180" x2="2938" y1="436.34" y2="436.34"/><line style="stroke:#181818;stroke-width:0.5;" x1="2180" x2="2938" y1="444.34" y2="444.34"/><ellipse cx="2190" cy="457.9884" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="391" x="2199" y="461.3351">claim(address token, uint256 amount, address recipient)</text><ellipse cx="2190" cy="474.2853" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="473" x="2199" y="477.632">delegateTo(string calldata operator, address token, uint256 amount)</text><ellipse cx="2190" cy="490.5822" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="508" x="2199" y="493.9289">undelegateFrom(string calldata operator, address token, uint256 amount)</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="2187" y="503.8791"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="734" x="2199" y="510.2257">_processRequest(address token, address sender, uint256 amount, Action action, string memory operator)</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="2187" y="520.1759"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="427" x="2199" y="526.5226">_sendMsgToImuachain(Action action, bytes memory actionArgs)</text></g><!--class LSTRestakingController--><g id="elem_LSTRestakingController"><rect codeLine="36" fill="#F1F1F1" height="96.8906" id="LSTRestakingController" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="523" x="2551.5" y="813.83"/><ellipse cx="2728.25" cy="829.83" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2731.2188,835.4706 Q2730.6406,835.7675 2730,835.9081 Q2729.3594,836.0644 2728.6563,836.0644 Q2726.1563,836.0644 2724.8281,834.4238 Q2723.5156,832.7675 2723.5156,829.6425 Q2723.5156,826.5175 2724.8281,824.8613 Q2726.1563,823.205 2728.6563,823.205 Q2729.3594,823.205 2730,823.3613 Q2730.6563,823.5175 2731.2188,823.8144 L2731.2188,826.5331 Q2730.5938,825.955 2730,825.6894 Q2729.4063,825.4081 2728.7813,825.4081 Q2727.4375,825.4081 2726.75,826.4863 Q2726.0625,827.5488 2726.0625,829.6425 Q2726.0625,831.7363 2726.75,832.8144 Q2727.4375,833.8769 2728.7813,833.8769 Q2729.4063,833.8769 2730,833.6113 Q2730.5938,833.33 2731.2188,832.7519 L2731.2188,835.4706 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="161" x="2748.75" y="834.6767">LSTRestakingController</text><line style="stroke:#181818;stroke-width:0.5;" x1="2552.5" x2="3073.5" y1="845.83" y2="845.83"/><line style="stroke:#181818;stroke-width:0.5;" x1="2552.5" x2="3073.5" y1="853.83" y2="853.83"/><ellipse cx="2562.5" cy="867.4784" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="280" x="2571.5" y="870.8251">deposit(address token, uint256 amount)</text><ellipse cx="2562.5" cy="883.7753" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="497" x="2571.5" y="887.122">withdrawPrincipleFromImuachain(address token, uint256 principleAmount)</text><ellipse cx="2562.5" cy="900.0722" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="480" x="2571.5" y="903.4189">withdrawRewardFromImuachain(address token, uint256 rewardAmount)</text></g><!--class NativeRestakingController--><g id="elem_NativeRestakingController"><rect codeLine="42" fill="#F1F1F1" height="113.1875" id="NativeRestakingController" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="468" x="2048" y="805.68"/><ellipse cx="2188.75" cy="821.68" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M2191.7188,827.3206 Q2191.1406,827.6175 2190.5,827.7581 Q2189.8594,827.9144 2189.1563,827.9144 Q2186.6563,827.9144 2185.3281,826.2738 Q2184.0156,824.6175 2184.0156,821.4925 Q2184.0156,818.3675 2185.3281,816.7113 Q2186.6563,815.055 2189.1563,815.055 Q2189.8594,815.055 2190.5,815.2113 Q2191.1563,815.3675 2191.7188,815.6644 L2191.7188,818.3831 Q2191.0938,817.805 2190.5,817.5394 Q2189.9063,817.2581 2189.2813,817.2581 Q2187.9375,817.2581 2187.25,818.3363 Q2186.5625,819.3988 2186.5625,821.4925 Q2186.5625,823.5863 2187.25,824.6644 Q2187.9375,825.7269 2189.2813,825.7269 Q2189.9063,825.7269 2190.5,825.4613 Q2191.0938,825.18 2191.7188,824.6019 L2191.7188,827.3206 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="178" x="2209.25" y="826.5267">NativeRestakingController</text><line style="stroke:#181818;stroke-width:0.5;" x1="2049" x2="2515" y1="837.68" y2="837.68"/><line style="stroke:#181818;stroke-width:0.5;" x1="2049" x2="2515" y1="845.68" y2="845.68"/><ellipse cx="2059" cy="859.3284" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="215" x="2068" y="862.6751">nativeDeposit(uint256 amount)</text><ellipse cx="2059" cy="875.6253" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="224" x="2068" y="878.972">nativeWithdraw(uint256 amount)</text><ellipse cx="2059" cy="891.9222" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="408" x="2068" y="895.2689">nativeDelegateTo(string calldata operator, uint256 amount)</text><ellipse cx="2059" cy="908.2191" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="442" x="2068" y="911.5657">nativeUndelegateFrom(string calldata operator, uint256 amount)</text></g><!--class ImuachainGateway--><g id="elem_ImuachainGateway"><rect codeLine="49" fill="#F1F1F1" height="276.1563" id="ImuachainGateway" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="602" x="7" y="331"/><ellipse cx="246.25" cy="347" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M249.2188,352.6406 Q248.6406,352.9375 248,353.0781 Q247.3594,353.2344 246.6563,353.2344 Q244.1563,353.2344 242.8281,351.5938 Q241.5156,349.9375 241.5156,346.8125 Q241.5156,343.6875 242.8281,342.0313 Q244.1563,340.375 246.6563,340.375 Q247.3594,340.375 248,340.5313 Q248.6563,340.6875 249.2188,340.9844 L249.2188,343.7031 Q248.5938,343.125 248,342.8594 Q247.4063,342.5781 246.7813,342.5781 Q245.4375,342.5781 244.75,343.6563 Q244.0625,344.7188 244.0625,346.8125 Q244.0625,348.9063 244.75,349.9844 Q245.4375,351.0469 246.7813,351.0469 Q247.4063,351.0469 248,350.7813 Q248.5938,350.5 249.2188,349.9219 L249.2188,352.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="115" x="266.75" y="351.8467">ImuachainGateway</text><line style="stroke:#181818;stroke-width:0.5;" x1="8" x2="608" y1="363" y2="363"/><line style="stroke:#181818;stroke-width:0.5;" x1="8" x2="608" y1="371" y2="371"/><ellipse cx="18" cy="384.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="383" x="27" y="387.9951">initialize(address payable ownerAddress)</text><ellipse cx="18" cy="400.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="194" x="27" y="404.292">markBootstrap(uint32 chainId)</text><ellipse cx="18" cy="417.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="53" x="27" y="420.5889">pause()</text><ellipse cx="18" cy="433.5391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="71" x="27" y="436.8857">unpause()</text><ellipse cx="18" cy="449.8359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="387" x="27" y="453.1826">_lzReceive(Origin calldata _origin, bytes calldata payload)</text><ellipse cx="18" cy="466.1328" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="509" x="27" y="469.4795">requestDeposit(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><ellipse cx="18" cy="482.4297" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="576" x="27" y="485.7764">requestWithdrawPrinciple(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><ellipse cx="18" cy="498.7266" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="570" x="27" y="502.0732">requestWithdrawReward(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><ellipse cx="18" cy="515.0234" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="536" x="27" y="518.3701">requestDelegateTo(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><ellipse cx="18" cy="531.3203" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="570" x="27" y="534.667">requestUndelegateFrom(uint32 srcChainId, uint64 lzNonce, bytes calldata payload)</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="15" y="544.6172"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="528" x="27" y="550.9639">_sendInterchainMsg(uint32 srcChainId, Action act, bytes memory actionArgs)</text><ellipse cx="18" cy="563.9141" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="352" x="27" y="567.2607">quote(uint32 srcChainid, bytes memory _message)</text><ellipse cx="18" cy="580.2109" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="292" x="27" y="583.5576">nextNonce(uint32 srcEid, bytes32 sender)</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="15" y="593.5078"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="491" x="27" y="599.8545">_verifyAndUpdateNonce(uint32 srcEid, bytes32 sender, uint64 nonce)</text></g><!--class ClientChainGateway--><g id="elem_ClientChainGateway"><rect codeLine="66" fill="#F1F1F1" height="227.2656" id="ClientChainGateway" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="468" x="2929" y="1117.39"/><ellipse cx="3090.25" cy="1133.39" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3093.2188,1139.0306 Q3092.6406,1139.3275 3092,1139.4681 Q3091.3594,1139.6244 3090.6563,1139.6244 Q3088.1563,1139.6244 3086.8281,1137.9838 Q3085.5156,1136.3275 3085.5156,1133.2025 Q3085.5156,1130.0775 3086.8281,1128.4213 Q3088.1563,1126.765 3090.6563,1126.765 Q3091.3594,1126.765 3092,1126.9213 Q3092.6563,1127.0775 3093.2188,1127.3744 L3093.2188,1130.0931 Q3092.5938,1129.515 3092,1129.2494 Q3091.4063,1128.9681 3090.7813,1128.9681 Q3089.4375,1128.9681 3088.75,1130.0463 Q3088.0625,1131.1088 3088.0625,1133.2025 Q3088.0625,1135.2963 3088.75,1136.3744 Q3089.4375,1137.4369 3090.7813,1137.4369 Q3091.4063,1137.4369 3092,1137.1713 Q3092.5938,1136.89 3093.2188,1136.3119 L3093.2188,1139.0306 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="137" x="3110.75" y="1138.2367">ClientChainGateway</text><line style="stroke:#181818;stroke-width:0.5;" x1="2930" x2="3396" y1="1149.39" y2="1149.39"/><line style="stroke:#181818;stroke-width:0.5;" x1="2930" x2="3396" y1="1157.39" y2="1157.39"/><ellipse cx="2940" cy="1171.0384" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="193" x="2949" y="1174.3851">initialize(address endpoint_)</text><ellipse cx="2940" cy="1187.3353" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="53" x="2949" y="1190.682">pause()</text><ellipse cx="2940" cy="1203.6322" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="71" x="2949" y="1206.9789">unpause()</text><ellipse cx="2940" cy="1219.9291" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="280" x="2949" y="1223.2757">deposit(address token, uint256 amount)</text><ellipse cx="2940" cy="1236.2259" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="289" x="2949" y="1239.5726">withdraw(address token, uint256 amount)</text><ellipse cx="2940" cy="1252.5228" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="188" x="2949" y="1255.8695">getBalance(address token)</text><ellipse cx="2940" cy="1268.8197" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="363" x="2949" y="1272.1664">transfer(address token, address to, uint256 amount)</text><ellipse cx="2940" cy="1285.1166" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="215" x="2949" y="1288.4632">nativeDeposit(uint256 amount)</text><ellipse cx="2940" cy="1301.4134" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="224" x="2949" y="1304.7601">nativeWithdraw(uint256 amount)</text><ellipse cx="2940" cy="1317.7103" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="408" x="2949" y="1321.057">nativeDelegateTo(string calldata operator, uint256 amount)</text><ellipse cx="2940" cy="1334.0072" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="442" x="2949" y="1337.3539">nativeUndelegateFrom(string calldata operator, uint256 amount)</text></g><!--class ClientGatewayLzReceiver--><g id="elem_ClientGatewayLzReceiver"><rect codeLine="80" fill="#F1F1F1" height="178.375" id="ClientGatewayLzReceiver" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="748" x="3565" y="379.89"/><ellipse cx="3848.25" cy="395.89" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3851.2188,401.5306 Q3850.6406,401.8275 3850,401.9681 Q3849.3594,402.1244 3848.6563,402.1244 Q3846.1563,402.1244 3844.8281,400.4838 Q3843.5156,398.8275 3843.5156,395.7025 Q3843.5156,392.5775 3844.8281,390.9213 Q3846.1563,389.265 3848.6563,389.265 Q3849.3594,389.265 3850,389.4213 Q3850.6563,389.5775 3851.2188,389.8744 L3851.2188,392.5931 Q3850.5938,392.015 3850,391.7494 Q3849.4063,391.4681 3848.7813,391.4681 Q3847.4375,391.4681 3846.75,392.5463 Q3846.0625,393.6088 3846.0625,395.7025 Q3846.0625,397.7963 3846.75,398.8744 Q3847.4375,399.9369 3848.7813,399.9369 Q3849.4063,399.9369 3850,399.6713 Q3850.5938,399.39 3851.2188,398.8119 L3851.2188,401.5306 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="173" x="3868.75" y="400.7367">ClientGatewayLzReceiver</text><line style="stroke:#181818;stroke-width:0.5;" x1="3566" x2="4312" y1="411.89" y2="411.89"/><line style="stroke:#181818;stroke-width:0.5;" x1="3566" x2="4312" y1="419.89" y2="419.89"/><ellipse cx="3576" cy="433.5384" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="387" x="3585" y="436.8851">_lzReceive(Origin calldata _origin, bytes calldata payload)</text><ellipse cx="3576" cy="449.8353" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="292" x="3585" y="453.182">nextNonce(uint32 srcEid, bytes32 sender)</text><ellipse cx="3576" cy="466.1322" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="491" x="3585" y="469.4789">_verifyAndUpdateNonce(uint32 srcEid, bytes32 sender, uint64 nonce)</text><ellipse cx="3576" cy="482.4291" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="655" x="3585" y="485.7757">afterReceiveDepositResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><ellipse cx="3576" cy="498.7259" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="722" x="3585" y="502.0726">afterReceiveWithdrawPrincipleResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><ellipse cx="3576" cy="515.0228" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="716" x="3585" y="518.3695">afterReceiveWithdrawRewardResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><ellipse cx="3576" cy="531.3197" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="664" x="3585" y="534.6664">afterReceiveDelegateResponse(bytes memory requestPayload, bytes calldata responsePayload)</text><ellipse cx="3576" cy="547.6166" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="681" x="3585" y="550.9632">afterReceiveUndelegateResponse(bytes memory requestPayload, bytes calldata responsePayload)</text></g><!--class ImuaCapsule--><g id="elem_ImuaCapsule"><rect codeLine="91" fill="#F1F1F1" height="325.0469" id="ImuaCapsule" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="1421" x="4418.5" y="699.75"/><ellipse cx="5084.25" cy="715.75" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M5087.2188,721.3906 Q5086.6406,721.6875 5086,721.8281 Q5085.3594,721.9844 5084.6563,721.9844 Q5082.1563,721.9844 5080.8281,720.3438 Q5079.5156,718.6875 5079.5156,715.5625 Q5079.5156,712.4375 5080.8281,710.7813 Q5082.1563,709.125 5084.6563,709.125 Q5085.3594,709.125 5086,709.2813 Q5086.6563,709.4375 5087.2188,709.7344 L5087.2188,712.4531 Q5086.5938,711.875 5086,711.6094 Q5085.4063,711.3281 5084.7813,711.3281 Q5083.4375,711.3281 5082.75,712.4063 Q5082.0625,713.4688 5082.0625,715.5625 Q5082.0625,717.6563 5082.75,718.7344 Q5083.4375,719.7969 5084.7813,719.7969 Q5085.4063,719.7969 5086,719.5313 Q5086.5938,719.25 5087.2188,718.6719 L5087.2188,721.3906 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="81" x="5104.75" y="720.5967">ImuaCapsule</text><line style="stroke:#181818;stroke-width:0.5;" x1="4419.5" x2="5838.5" y1="731.75" y2="731.75"/><line style="stroke:#181818;stroke-width:0.5;" x1="4419.5" x2="5838.5" y1="739.75" y2="739.75"/><ellipse cx="4429.5" cy="753.3984" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="533" x="4438.5" y="756.7451">initialize(address gateway_, address capsuleOwner_, address beaconOracle_)</text><ellipse cx="4429.5" cy="769.6953" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="651" x="4438.5" y="773.042">verifyDepositProof(bytes32[] calldata validatorContainer, ValidatorContainerProof calldata proof)</text><ellipse cx="4429.5" cy="785.9922" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="1395" x="4438.5" y="789.3389">verifyPartialWithdrawalProof(bytes32[] calldata validatorContainer, ValidatorContainerProof calldata validatorProof, bytes32[] calldata withdrawalContainer, WithdrawalContainerProof calldata withdrawalProof)</text><ellipse cx="4429.5" cy="802.2891" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="1377" x="4438.5" y="805.6357">verifyFullWithdrawalProof(bytes32[] calldata validatorContainer, ValidatorContainerProof calldata validatorProof, bytes32[] calldata withdrawalContainer, WithdrawalContainerProof calldata withdrawalProof)</text><ellipse cx="4429.5" cy="818.5859" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="309" x="4438.5" y="821.9326">withdraw(uint256 amount, address recipient)</text><ellipse cx="4429.5" cy="834.8828" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="435" x="4438.5" y="838.2295">updatePrincipleBalance(uint256 lastlyUpdatedPrincipleBalance)</text><ellipse cx="4429.5" cy="851.1797" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="421" x="4438.5" y="854.5264">updateWithdrawableBalance(uint256 unlockPrincipleAmount)</text><ellipse cx="4429.5" cy="867.4766" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="267" x="4438.5" y="870.8232">capsuleWithdrawalCredentials() : bytes</text><ellipse cx="4429.5" cy="883.7734" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="356" x="4438.5" y="887.1201">getBeaconBlockRoot(uint256 timestamp) : bytes32</text><ellipse cx="4429.5" cy="900.0703" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="419" x="4438.5" y="903.417">getRegisteredValidatorByPubkey(bytes32 pubkey) : Validator</text><ellipse cx="4429.5" cy="916.3672" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="388" x="4438.5" y="919.7139">getRegisteredValidatorByIndex(uint256 index) : Validator</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="4426.5" y="929.6641"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="694" x="4438.5" y="936.0107">_verifyValidatorContainer(bytes32[] calldata validatorContainer, ValidatorContainerProof calldata proof)</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="4426.5" y="945.9609"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="739" x="4438.5" y="952.3076">_verifyWithdrawalContainer(bytes32[] calldata withdrawalContainer, WithdrawalContainerProof calldata proof)</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="4426.5" y="962.2578"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="597" x="4438.5" y="968.6045">_isActivatedAtEpoch(bytes32[] calldata validatorContainer, uint256 atTimestamp) : bool</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="4426.5" y="978.5547"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="496" x="4438.5" y="984.9014">_isStaleProof(Validator storage validator, uint256 proofTimestamp) : bool</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="4426.5" y="994.8516"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="437" x="4438.5" y="1001.1982">_hasFullyWithdrawn(bytes32[] calldata validatorContainer) : bool</text><rect fill="#F24D5C" height="6" style="stroke:#C82930;stroke-width:1.0;" width="6" x="4426.5" y="1011.1484"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="339" x="4438.5" y="1017.4951">_timestampToEpoch(uint256 timestamp) : uint64</text></g><!--class Bootstrap--><g id="elem_Bootstrap"><rect codeLine="111" fill="#F1F1F1" height="390.2344" id="Bootstrap" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="1297" x="715.5" y="667.16"/><ellipse cx="1325.75" cy="683.16" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M1328.7188,688.8006 Q1328.1406,689.0975 1327.5,689.2381 Q1326.8594,689.3944 1326.1563,689.3944 Q1323.6563,689.3944 1322.3281,687.7538 Q1321.0156,686.0975 1321.0156,682.9725 Q1321.0156,679.8475 1322.3281,678.1913 Q1323.6563,676.535 1326.1563,676.535 Q1326.8594,676.535 1327.5,676.6913 Q1328.1563,676.8475 1328.7188,677.1444 L1328.7188,679.8631 Q1328.0938,679.285 1327.5,679.0194 Q1326.9063,678.7381 1326.2813,678.7381 Q1324.9375,678.7381 1324.25,679.8163 Q1323.5625,680.8788 1323.5625,682.9725 Q1323.5625,685.0663 1324.25,686.1444 Q1324.9375,687.2069 1326.2813,687.2069 Q1326.9063,687.2069 1327.5,686.9413 Q1328.0938,686.66 1328.7188,686.0819 L1328.7188,688.8006 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="68" x="1346.25" y="688.0067">Bootstrap</text><line style="stroke:#181818;stroke-width:0.5;" x1="716.5" x2="2011.5" y1="699.16" y2="699.16"/><line style="stroke:#181818;stroke-width:0.5;" x1="716.5" x2="2011.5" y1="707.16" y2="707.16"/><ellipse cx="726.5" cy="720.8084" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="1271" x="735.5" y="724.1551">initialize(address owner, uint256 spawnTime_, uint256 offsetDuration_, address payable owner_, address[] calldata whitelistTokens_, address customProxyAdmin_)</text><ellipse cx="726.5" cy="737.1053" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="53" x="735.5" y="740.452">pause()</text><ellipse cx="726.5" cy="753.4022" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="71" x="735.5" y="756.7489">unpause()</text><ellipse cx="726.5" cy="769.6991" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="255" x="735.5" y="773.0457">setSpawnTime(uint256 _spawnTime)</text><ellipse cx="726.5" cy="785.9959" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="294" x="735.5" y="789.3426">setOffsetDuration(uint256 _offsetDuration)</text><ellipse cx="726.5" cy="802.2928" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="245" x="735.5" y="805.6395">addWhitelistToken(address _token)</text><ellipse cx="726.5" cy="818.5897" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="271" x="735.5" y="821.9364">removeWhitelistToken(address _token)</text><ellipse cx="726.5" cy="834.8866" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="992" x="735.5" y="838.2332">registerOperator(string calldata operatorImuachainAddress, string calldata name, Commission memory commission, bytes32 consensusPublicKey)</text><ellipse cx="726.5" cy="851.1834" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="200" x="735.5" y="854.5301">replaceKey(bytes32 newKey)</text><ellipse cx="726.5" cy="867.4803" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="209" x="735.5" y="870.827">updateRate(uint256 newRate)</text><ellipse cx="726.5" cy="883.7772" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="280" x="735.5" y="887.1239">deposit(address token, uint256 amount)</text><ellipse cx="726.5" cy="900.0741" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="438" x="735.5" y="903.4207">withdrawPrincipleFromImuachain(address token, uint256 amount)</text><ellipse cx="726.5" cy="916.3709" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="432" x="735.5" y="919.7176">withdrawRewardFromImuachain(address token, uint256 amount)</text><ellipse cx="726.5" cy="932.6678" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="391" x="735.5" y="936.0145">claim(address token, uint256 amount, address recipient)</text><ellipse cx="726.5" cy="948.9647" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="473" x="735.5" y="952.3114">delegateTo(string calldata operator, address token, uint256 amount)</text><ellipse cx="726.5" cy="965.2616" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="508" x="735.5" y="968.6082">undelegateFrom(string calldata operator, address token, uint256 amount)</text><ellipse cx="726.5" cy="981.5584" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="743" x="735.5" y="984.9051">setClientChainGatewayLogic(address _clientChainGatewayLogic, bytes calldata _clientChainInitializationData)</text><ellipse cx="726.5" cy="997.8553" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="144" x="735.5" y="1001.202">getOperatorsCount()</text><ellipse cx="726.5" cy="1014.1522" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="151" x="735.5" y="1017.4989">getDepositorsCount()</text><ellipse cx="726.5" cy="1030.4491" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="203" x="735.5" y="1033.7957">getWhitelistedTokensCount()</text><ellipse cx="726.5" cy="1046.7459" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="298" x="735.5" y="1050.0926">getWhitelistedTokenAtIndex(uint256 index)</text></g><!--class IClientChainGateway--><g id="elem_IClientChainGateway"><rect fill="#F1F1F1" height="48" id="IClientChainGateway" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="172" x="3252" y="838.28"/><ellipse cx="3267" cy="854.28" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M3269.9688,859.9206 Q3269.3906,860.2175 3268.75,860.3581 Q3268.1094,860.5144 3267.4063,860.5144 Q3264.9063,860.5144 3263.5781,858.8738 Q3262.2656,857.2175 3262.2656,854.0925 Q3262.2656,850.9675 3263.5781,849.3113 Q3264.9063,847.655 3267.4063,847.655 Q3268.1094,847.655 3268.75,847.8113 Q3269.4063,847.9675 3269.9688,848.2644 L3269.9688,850.9831 Q3269.3438,850.405 3268.75,850.1394 Q3268.1563,849.8581 3267.5313,849.8581 Q3266.1875,849.8581 3265.5,850.9363 Q3264.8125,851.9988 3264.8125,854.0925 Q3264.8125,856.1863 3265.5,857.2644 Q3266.1875,858.3269 3267.5313,858.3269 Q3268.1563,858.3269 3268.75,858.0613 Q3269.3438,857.78 3269.9688,857.2019 L3269.9688,859.9206 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="140" x="3281" y="859.1267">IClientChainGateway</text><line style="stroke:#181818;stroke-width:0.5;" x1="3253" x2="3423" y1="870.28" y2="870.28"/><line style="stroke:#181818;stroke-width:0.5;" x1="3253" x2="3423" y1="878.28" y2="878.28"/></g><!--class BootstrapLzReceiver--><g id="elem_BootstrapLzReceiver"><rect fill="#F1F1F1" height="48" id="BootstrapLzReceiver" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="175" x="1092.5" y="445.08"/><ellipse cx="1107.5" cy="461.08" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1.0;"/><path d="M1110.4688,466.7206 Q1109.8906,467.0175 1109.25,467.1581 Q1108.6094,467.3144 1107.9063,467.3144 Q1105.4063,467.3144 1104.0781,465.6738 Q1102.7656,464.0175 1102.7656,460.8925 Q1102.7656,457.7675 1104.0781,456.1113 Q1105.4063,454.455 1107.9063,454.455 Q1108.6094,454.455 1109.25,454.6113 Q1109.9063,454.7675 1110.4688,455.0644 L1110.4688,457.7831 Q1109.8438,457.205 1109.25,456.9394 Q1108.6563,456.6581 1108.0313,456.6581 Q1106.6875,456.6581 1106,457.7363 Q1105.3125,458.7988 1105.3125,460.8925 Q1105.3125,462.9863 1106,464.0644 Q1106.6875,465.1269 1108.0313,465.1269 Q1108.6563,465.1269 1109.25,464.8613 Q1109.8438,464.58 1110.4688,464.0019 L1110.4688,466.7206 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="143" x="1121.5" y="465.9267">BootstrapLzReceiver</text><line style="stroke:#181818;stroke-width:0.5;" x1="1093.5" x2="1266.5" y1="477.08" y2="477.08"/><line style="stroke:#181818;stroke-width:0.5;" x1="1093.5" x2="1266.5" y1="485.08" y2="485.08"/></g><!--reverse link PausableUpgradeable to BaseRestakingController--><g id="link_PausableUpgradeable_BaseRestakingController"><path codeLine="135" d="M2650.2246,287.2118 C2634.4746,318.4418 2613.89,359.25 2591.38,403.88 " fill="none" id="PausableUpgradeable-backto-BaseRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2658.33,271.14,2644.8674,284.51,2655.5819,289.9136,2658.33,271.14" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link OAppSenderUpgradeable to BaseRestakingController--><g id="link_OAppSenderUpgradeable_BaseRestakingController"><path codeLine="136" d="M2460.2337,286.9452 C2477.2537,318.1752 2499.66,359.25 2523.99,403.88 " fill="none" id="OAppSenderUpgradeable-backto-BaseRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2451.62,271.14,2454.9653,289.8164,2465.5021,284.074,2451.62,271.14" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IBaseRestakingController to BaseRestakingController--><g id="link_IBaseRestakingController_BaseRestakingController"><path codeLine="137" d="M2046.9416,277.7228 C2131.1816,309.0428 2266.82,359.46 2386.5,403.96 " fill="none" id="IBaseRestakingController-backto-BaseRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2030.07,271.45,2044.8507,283.3467,2049.0326,272.0989,2030.07,271.45" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ClientChainGatewayStorage to BaseRestakingController--><g id="link_ClientChainGatewayStorage_BaseRestakingController"><path codeLine="138" d="M3698.2161,257.43 C3554.5361,268.8 3315.77,291.34 3099,331 C2994.34,350.15 2879.92,378.16 2783.46,403.87 " fill="none" id="ClientChainGatewayStorage-backto-BaseRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3716.16,256.01,3697.7428,251.4487,3698.6894,263.4113,3716.16,256.01" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link PausableUpgradeable to LSTRestakingController--><g id="link_PausableUpgradeable_LSTRestakingController"><path codeLine="140" d="M2779.5916,265.6339 C2854.2716,279.4039 2933.31,299.49 2957,331 C3069.93,481.22 2923.78,714.26 2850.42,813.7 " fill="none" id="PausableUpgradeable-backto-LSTRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2761.89,262.37,2778.5036,271.5345,2780.6796,259.7334,2761.89,262.37" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IBaseRestakingController to ILSTRestakingController--><g id="link_IBaseRestakingController_ILSTRestakingController"><path codeLine="141" d="M1880.6979,277.8786 C1844.3679,291.9686 1817.07,305.61 1782,331 C1736.74,363.77 1695.41,415.09 1673.48,444.76 " fill="none" id="IBaseRestakingController-backto-ILSTRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="1897.48,271.37,1878.5284,272.2846,1882.8675,283.4727,1897.48,271.37" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ILSTRestakingController to LSTRestakingController--><g id="link_ILSTRestakingController_LSTRestakingController"><path codeLine="142" d="M1692.525,506.0795 C1725.225,538.1295 1776.2,581.95 1843,607.16 C2131.41,716 2244.13,562.25 2534,667.16 C2622.78,699.29 2709.57,767.08 2762.18,813.41 " fill="none" id="ILSTRestakingController-backto-LSTRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="1679.67,493.48,1688.3252,510.3645,1696.7249,501.7945,1679.67,493.48" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link BaseRestakingController to LSTRestakingController--><g id="link_BaseRestakingController_LSTRestakingController"><path codeLine="143" d="M2610.4625,549.3367 C2661.3425,627.6967 2736.42,743.34 2781.93,813.43 " fill="none" id="BaseRestakingController-backto-LSTRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2600.66,534.24,2605.4302,552.6042,2615.4947,546.0692,2600.66,534.24" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link PausableUpgradeable to NativeRestakingController--><g id="link_PausableUpgradeable_NativeRestakingController"><path codeLine="145" d="M2560.5211,271.2165 C2472.0611,287.1765 2219.65,262.9 2161,331 C2042.23,468.92 2170.91,699.77 2241.74,805.42 " fill="none" id="PausableUpgradeable-backto-NativeRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2578.19,267.78,2559.3756,265.3268,2561.6666,277.1061,2578.19,267.78" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IBaseRestakingController to INativeRestakingController--><g id="link_IBaseRestakingController_INativeRestakingController"><path codeLine="146" d="M1967,289.14 C1967,331.3 1967,402.18 1967,444.59 " fill="none" id="IBaseRestakingController-backto-INativeRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="1967,271.14,1961,289.14,1973,289.14,1967,271.14" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link INativeRestakingController to NativeRestakingController--><g id="link_INativeRestakingController_NativeRestakingController"><path codeLine="147" d="M1997.0481,507.4006 C2045.9181,568.1006 2166.93,718.38 2237.02,805.42 " fill="none" id="INativeRestakingController-backto-NativeRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="1985.76,493.38,1992.3746,511.1633,2001.7217,503.6379,1985.76,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link BaseRestakingController to NativeRestakingController--><g id="link_BaseRestakingController_NativeRestakingController"><path codeLine="148" d="M2503.1682,548.9302 C2449.7582,624.3602 2372.37,733.65 2321.62,805.33 " fill="none" id="BaseRestakingController-backto-NativeRestakingController" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2513.57,534.24,2498.2715,545.463,2508.065,552.3975,2513.57,534.24" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link PausableUpgradeable to ImuachainGateway--><g id="link_PausableUpgradeable_ImuachainGateway"><path codeLine="150" d="M2560.2368,271.4287 C1821.7268,361.9087 1625.37,231.63 888,331 C796.48,343.34 698.28,363.73 609.18,385.07 " fill="none" id="PausableUpgradeable-backto-ImuachainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2578.01,268.58,2559.2873,265.5043,2561.1864,277.353,2578.01,268.58" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link OwnableUpgradeable to ImuachainGateway--><g id="link_OwnableUpgradeable_ImuachainGateway"><path codeLine="151" d="M632.1141,280.3717 C605.8541,295.5817 584.77,307.79 545.2,330.71 " fill="none" id="OwnableUpgradeable-backto-ImuachainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="647.69,271.35,629.1069,275.1797,635.1213,285.5637,647.69,271.35" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IImuachainGateway to ImuachainGateway--><g id="link_IImuachainGateway_ImuachainGateway"><path codeLine="152" d="M202.5036,286.7032 C211.3536,301.9332 214.72,307.7 228.11,330.73 " fill="none" id="IImuachainGateway-backto-ImuachainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="193.46,271.14,197.3159,289.7177,207.6913,283.6887,193.46,271.14" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ImuachainGatewayStorage to ImuachainGateway--><g id="link_ImuachainGatewayStorage_ImuachainGateway"><path codeLine="153" d="M435.4883,285.8866 C424.8283,301.1166 420.23,307.7 404.11,330.73 " fill="none" id="ImuachainGatewayStorage-backto-ImuachainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="445.81,271.14,430.5728,282.4461,440.4039,289.3272,445.81,271.14" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link OAppUpgradeable to ImuachainGateway--><g id="link_OAppUpgradeable_ImuachainGateway"><path codeLine="154" d="M2341.907,163.6061 C2231.777,178.2961 426.98,149.12 344,223 C313.65,250.03 300.8,290.5 296.67,330.8 " fill="none" id="OAppUpgradeable-backto-ImuachainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2359.61,160.35,2340.8216,157.7051,2342.9923,169.5071,2359.61,160.35" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link PausableUpgradeable to ClientChainGateway--><g id="link_PausableUpgradeable_ClientChainGateway"><path codeLine="156" d="M2779.6384,263.0316 C2866.0084,276.3916 2969.16,297.65 3003,331 C3113.08,439.49 3062.49,515.45 3092,667.16 C3122.07,821.73 3142.27,1002.76 3153.21,1116.92 " fill="none" id="PausableUpgradeable-backto-ClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2761.85,260.28,2778.7213,268.9611,2780.5556,257.1021,2761.85,260.28" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link OwnableUpgradeable to ClientChainGateway--><g id="link_OwnableUpgradeable_ClientChainGateway"><path codeLine="157" d="M681.5877,288.9399 C663.0977,408.8899 588.15,941.03 698,1057.39 C773.92,1137.82 2327.53,1200.71 2928.5,1222.11 " fill="none" id="OwnableUpgradeable-backto-ClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="684.33,271.15,675.6578,288.0258,687.5177,289.854,684.33,271.15" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ClientChainGatewayStorage to ClientChainGateway--><g id="link_ClientChainGatewayStorage_ClientChainGateway"><path codeLine="158" d="M3957.8757,250.9573 C4090.5257,256.2473 4281.86,273.09 4331,331 C4410.41,424.59 4389.48,499.25 4331,607.16 C4142.72,954.55 3680.48,1116.27 3397.21,1185.02 " fill="none" id="ClientChainGatewayStorage-backto-ClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3939.89,250.24,3957.6366,256.9525,3958.1148,244.962,3939.89,250.24" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link LSTRestakingController to ClientChainGateway--><g id="link_LSTRestakingController_ClientChainGateway"><path codeLine="159" d="M2870.8274,923.8715 C2920.3974,975.8115 2989.1,1047.81 3055.17,1117.04 " fill="none" id="LSTRestakingController-backto-ClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2858.4,910.85,2866.4869,928.014,2875.1679,919.7291,2858.4,910.85" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link NativeRestakingController to ClientChainGateway--><g id="link_NativeRestakingController_ClientChainGateway"><path codeLine="160" d="M2354.0354,931.2123 C2402.4354,974.5823 2461.33,1021.2 2534,1057.39 C2657.92,1119.11 2806.92,1161.26 2928.8,1188.44 " fill="none" id="NativeRestakingController-backto-ClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2340.63,919.2,2350.0313,935.6807,2358.0395,926.7438,2340.63,919.2" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ClientGatewayLzReceiver to ClientChainGateway--><g id="link_ClientGatewayLzReceiver_ClientChainGateway"><path codeLine="161" d="M3867.2969,573.3866 C3780.1369,695.3966 3621.35,902.83 3442,1057.39 C3417.73,1078.31 3390.65,1098.37 3363.18,1116.94 " fill="none" id="ClientGatewayLzReceiver-backto-ClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3877.76,558.74,3862.4147,569.8989,3872.1791,576.8743,3877.76,558.74" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IClientChainGateway to ClientChainGateway--><g id="link_IClientChainGateway_ClientChainGateway"><path codeLine="162" d="M3319.0364,903.0244 C3297.2664,948.6344 3255.78,1035.58 3216.94,1116.98 " fill="none" id="IClientChainGateway-backto-ClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3326.79,886.78,3313.6216,900.4399,3324.4512,905.609,3326.79,886.78" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link Initializable to ClientChainGateway--><g id="link_Initializable_ClientChainGateway"><path codeLine="163" d="M3168.6764,511.2394 C3167.9364,604.0194 3165.33,934.66 3163.89,1117.12 " fill="none" id="Initializable-backto-ClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3168.82,493.24,3162.6766,511.1916,3174.6762,511.2873,3168.82,493.24" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link PausableUpgradeable to ClientGatewayLzReceiver--><g id="link_PausableUpgradeable_ClientGatewayLzReceiver"><path codeLine="165" d="M2779.6942,249.8665 C2940.5342,253.9465 3265.19,269.35 3547,331 C3602.55,343.16 3661.08,360.83 3715.49,379.48 " fill="none" id="PausableUpgradeable-backto-ClientGatewayLzReceiver" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2761.7,249.41,2779.5421,255.8645,2779.8464,243.8684,2761.7,249.41" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link OAppReceiverUpgradeable to ClientGatewayLzReceiver--><g id="link_OAppReceiverUpgradeable_ClientGatewayLzReceiver"><path codeLine="166" d="M3429.6115,277.7499 C3498.6115,303.5299 3596.76,340.21 3701.7,379.42 " fill="none" id="OAppReceiverUpgradeable-backto-ClientGatewayLzReceiver" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3412.75,271.45,3427.5116,283.3704,3431.7115,272.1293,3412.75,271.45" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ClientChainGatewayStorage to ClientGatewayLzReceiver--><g id="link_ClientChainGatewayStorage_ClientGatewayLzReceiver"><path codeLine="167" d="M3847.7772,287.2109 C3860.7772,312.9809 3874.47,340.14 3894.34,379.54 " fill="none" id="ClientChainGatewayStorage-backto-ClientGatewayLzReceiver" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3839.67,271.14,3842.4202,289.9133,3853.1341,284.5085,3839.67,271.14" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link Initializable to ImuaCapsule--><g id="link_Initializable_ImuaCapsule"><path codeLine="169" d="M3178.2283,510.8419 C3186.0783,542.2219 3200.4,579.83 3239,607.16 C3288.6,642.28 3903.15,720.72 4418.22,781.23 " fill="none" id="Initializable-backto-ImuaCapsule" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3173.86,493.38,3172.4076,512.298,3184.0489,509.3858,3173.86,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ImuaCapsuleStorage to ImuaCapsule--><g id="link_ImuaCapsuleStorage_ImuaCapsule"><path codeLine="170" d="M5050.1888,510.9415 C5059.2888,551.4115 5074.8,620.35 5092.57,699.32 " fill="none" id="ImuaCapsuleStorage-backto-ImuaCapsule" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="5046.24,493.38,5044.335,512.2578,5056.0427,509.6252,5046.24,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IImuaCapsule to ImuaCapsule--><g id="link_IImuaCapsule_ImuaCapsule"><path codeLine="171" d="M5207.8112,510.9415 C5198.7112,551.4115 5183.2,620.35 5165.43,699.32 " fill="none" id="IImuaCapsule-backto-ImuaCapsule" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="5211.76,493.38,5201.9573,509.6252,5213.665,512.2578,5211.76,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link GatewayStorage to BootstrapStorage--><g id="link_GatewayStorage_BootstrapStorage"><path codeLine="173" d="M3229,73.27 C3229,90.94 3229,97.14 3229,114.8 " fill="none" id="GatewayStorage-backto-BootstrapStorage" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3229,55.27,3223,73.27,3235,73.27,3229,55.27" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link BootstrapStorage to ClientChainGatewayStorage--><g id="link_BootstrapStorage_ClientChainGatewayStorage"><path codeLine="174" d="M3324.1146,156.8364 C3428.3246,175.2764 3596.71,205.07 3716.15,226.21 " fill="none" id="BootstrapStorage-backto-ClientChainGatewayStorage" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3306.39,153.7,3323.0692,162.7446,3325.1601,150.9282,3306.39,153.7" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link OAppUpgradeable to OAppSenderUpgradeable--><g id="link_OAppUpgradeable_OAppSenderUpgradeable"><path codeLine="176" d="M2439,181.27 C2439,198.94 2439,205.14 2439,222.8 " fill="none" id="OAppUpgradeable-backto-OAppSenderUpgradeable" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2439,163.27,2433,181.27,2445,181.27,2439,163.27" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link OAppUpgradeable to OAppReceiverUpgradeable--><g id="link_OAppUpgradeable_OAppReceiverUpgradeable"><path codeLine="177" d="M2536.1295,151.3097 C2699.2195,170.2797 3054.78,211.65 3240.69,233.28 " fill="none" id="OAppUpgradeable-backto-OAppReceiverUpgradeable" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2518.25,149.23,2535.4362,157.2695,2536.8227,145.3499,2518.25,149.23" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link PausableUpgradeable to Bootstrap--><g id="link_PausableUpgradeable_Bootstrap"><path codeLine="179" d="M2560.5393,271.3121 C2481.3293,282.9321 1904.17,279.35 1843,331 C1746.18,412.77 1846.94,504.96 1772,607.16 C1756.46,628.36 1738.56,648.19 1719.13,666.66 " fill="none" id="PausableUpgradeable-backto-Bootstrap" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="2578.27,268.21,2559.5053,265.4019,2561.5734,277.2223,2578.27,268.21" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link OwnableUpgradeable to Bootstrap--><g id="link_OwnableUpgradeable_Bootstrap"><path codeLine="180" d="M701.4689,288.0729 C722.7689,349.7129 782.61,503.63 888,607.16 C909.55,628.33 933.23,648.2 958.15,666.76 " fill="none" id="OwnableUpgradeable-backto-Bootstrap" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="695.59,271.06,695.7979,290.0325,707.1399,286.1133,695.59,271.06" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link Initializable to Bootstrap--><g id="link_Initializable_Bootstrap"><path codeLine="181" d="M3129.1938,505.078 C3091.3338,537.308 3031.28,582.6 2957,607.16 C2565.43,736.64 2437.19,595.78 2031,667.16 C2024.91,668.23 2018.8,669.33 2012.67,670.47 " fill="none" id="Initializable-backto-Bootstrap" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3142.9,493.41,3125.3045,500.5092,3133.0832,509.6467,3142.9,493.41" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ITokenWhitelister to Bootstrap--><g id="link_ITokenWhitelister_Bootstrap"><path codeLine="182" d="M1376.5266,511.3685 C1375.2766,546.3185 1373.43,597.65 1370.96,666.78 " fill="none" id="ITokenWhitelister-backto-Bootstrap" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="1377.17,493.38,1370.5305,511.154,1382.5228,511.583,1377.17,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link ILSTRestakingController to Bootstrap--><g id="link_ILSTRestakingController_Bootstrap"><path codeLine="183" d="M1628.7586,507.7864 C1602.5786,542.7364 1561.46,597.65 1509.68,666.78 " fill="none" id="ILSTRestakingController-backto-Bootstrap" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="1639.55,493.38,1623.9564,504.1893,1633.5607,511.3836,1639.55,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IValidatorRegistry to Bootstrap--><g id="link_IValidatorRegistry_Bootstrap"><path codeLine="184" d="M1003.6225,508.6159 C1020.5625,536.9259 1042.27,570.24 1075,607.16 C1092.84,627.28 1112.21,647.36 1132.2,666.91 " fill="none" id="IValidatorRegistry-backto-Bootstrap" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="994.38,493.17,998.4738,511.6968,1008.7711,505.5351,994.38,493.17" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link BootstrapLzReceiver to Bootstrap--><g id="link_BootstrapLzReceiver_Bootstrap"><path codeLine="185" d="M1198.6217,509.668 C1215.0617,544.618 1240,597.65 1272.52,666.78 " fill="none" id="BootstrapLzReceiver-backto-Bootstrap" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="1190.96,493.38,1193.1923,512.2219,1204.051,507.1141,1190.96,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link BootstrapStorage to Bootstrap--><g id="link_BootstrapStorage_Bootstrap"><path codeLine="186" d="M3133.7456,142.2503 C2881.1656,148.5703 2099.64,171.75 1847,223 C1706.07,251.59 1639.08,224.91 1542,331 C1456.45,424.5 1511.48,487.07 1471,607.16 C1464.43,626.67 1457.04,646.79 1449.3,666.77 " fill="none" id="BootstrapStorage-backto-Bootstrap" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3151.74,141.8,3133.5955,136.2521,3133.8957,148.2484,3151.74,141.8" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IOAppReceiver to IClientChainGateway--><g id="link_IOAppReceiver_IClientChainGateway"><path codeLine="188" d="M3324.5802,511.3668 C3327.2502,580.9468 3334.45,768.68 3337.11,838.1 " fill="none" id="IOAppReceiver-backto-IClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3323.89,493.38,3318.5846,511.5968,3330.5758,511.1367,3323.89,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--reverse link IOAppCore to IClientChainGateway--><g id="link_IOAppCore_IClientChainGateway"><path codeLine="189" d="M3462.6941,510.3414 C3437.9741,579.9214 3370.9,768.68 3346.24,838.1 " fill="none" id="IOAppCore-backto-IClientChainGateway" style="stroke:#181818;stroke-width:1.0;"/><polygon fill="none" points="3468.72,493.38,3457.0403,508.3327,3468.3478,512.35,3468.72,493.38" style="stroke:#181818;stroke-width:1.0;"/></g><!--SRC=[tLbBSzis4BxhL_0eJ-9BqkRGwKMMdLOJDz98SZpJJaS3aP2CCKWm81XPJllVko0U14c0Ockx1_iY4_XsiRluTl4YcS1yXs8SpQuUMCmu_WK9V4374QbxhbVh9Iu4kYVPVieomHcbcBk8BwYamcq0PLEK5oN5Rki5Al09JTVeYFdlcBCbZZ7vrkoSKe8pCRr392lSzlaaptrusJu5dzospqW0x8GL5nZ5BAi6cdCKDxsuMl-wcAykSSuAqd1wpUvnTdj71AQa42rJSioHO7o9zz37ZwCHsi8lYaKKKrGKqGAL1TfIV9FlEKgm_BSDcH-o4uXPHWH1b3nwnI4QVTgXUuMp1FCJ81Ff7fWcqGeSHljExmLZGZRaWNvdcliLDIaR1cag6i18c-5dO_Hz5C7V6m2HT8oIX6C02pctRwCIflJTZnyYbB8o4syZqyrnJ79fzPaIJZ35Up1hpSPW3EYFOaHfWWIAc6P2BUpLhHMLcL7raRFqQShEDpbdCO2M-6i9ZhUTCvz5nOAkivDO49P5gFfv6sbRKfmoVhIMcA6aaj-A_PhfIHgtfBT7WGiZh1edV5-SZVusy-ClEsPw4fL_uo51F11nbt1qM831C4-qYgMnAw0WDzY9JzCI2CUJ0Mfu1JGwh6_1AcJSoog0BZ9ZhrSASgijwiDS3c5ZZ_pDK1AsTLX_cvbgd2IcNDNXoz5H5fG8Au4lq9j8tIii9WgoqQQbYD_RIZBF9fIgXM6i-tCeg7XiqwZnkQ6FkegDvvpiILOxis5LWo6eRGV3A4E9Li1L1hdpy_vTLF2uic6Mg51y-26YZvzP5kDXwcxRF7rknOgspwNLeTKpQMmHxObQLH6QoKKubl9GZdmgwyBKKvKgAx-MJ7JcX2GjiKqAB8LrngGGVX2LiOxWbHMItrMvDIR3lgCeKppBjf12oG116vDCOfsSwowZFNa7gd86UZO3ikU_bDv1u3qM5uWYwN83hhiXqRDYXtbetHAijugzccAg9bRFwRMfP7RfU79n-rUCVVDqnbPgq0xILDkjPmgMnnnqut4ZlNIfM2YJEwvmBRHekEHgxrJP5nv75TqN6iImyONKrsHzdW7S3O7UbVSKfRsY-APEyLZ9m4a8SwTvMvtNr57DL3382B8xQYejE6ExiQRX7t_M5dup6mov1ODAATdvfTDMYJlf8ZyRmomGbyQRAKVqUGUqGbMxpITNxy4ExIY-xJQsD1-Q5ZUS-bZIL-FHmL_PmoUuCWTjsDO8iouPSI2neCUR2fIqKGqLrk9jLqkPKHRVBxn73Krrns6E4x0FQ0PxtEWd5R9wuQnIu4BgMyAks0uXI2fF6MbkHTw_iqBgDWA3OZjX5yT5kRt7HpFJKLvzIccBwPUVGSruiECJ-TMLFjyeZdM9yX9PQaRpi6VyVCnh34WAkQ_y9kan4LSvY- -6EcOPAjQDkMECMjqhWIXM6MdZ00VowdwZLkgUPW4QKdU72fdgDZhPC4jRMYpHuCXVkUjbe5hvwtN0dk1wbW5sQCIboC54wSkcRcExNO75PGbR9zYoEol3WFEa1JSeOAxngakzmbs2IeXp2h5_E4wIb6H3TinWsyfORUEoiOxKg7d3ZnhQzCyir4bosp3JHcpZRfivBDMm6nc4LEbiBYk3crDzdDSHj5T15fUX5CvcKvQcf2ZauKfl5sBRKky9vVOH8bmMK7SeYJz1hJ7Mv1J5-9DJYp9ygFjLULqYKOSK-kNtsVzpHOIUT3FKa7vzbusIpTqJw3NRaxWcN-m7TFQHBh2-uvQfS0dTzUdH4BUOIaF6TOVU1dfwwllwHANwAS14-1OuMT4yL_ZHpt-TduTkcaVUA_T-aT0RIhzKy4QzNopiKU19gD-wq4DIeBrtfDw10Z9XTyAFGYSywX4CN_6U6B9tnB1OsBlMorxt6SaBQpq8UZ7U1nZ_eCt79oycx46Nlq4lFD0JxFT8z17I0pynrK6JkbSu8VpCSvaNnBgFW47KgM1tR1i5tlxwXGR5lYlcSy9vK-wywNKGpWjqfs_ahO7jHybsGoYIzbNHIt7_gseGwdjRxLbNZ8d1RAWHde0wdUsdwmwWBoCSMFkPkmsm_xKvxu7ufmUeuGPArs5V6fZN_o3a7m00]--></g></svg>
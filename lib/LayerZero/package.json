{"name": "@layerzerolabs/contracts", "version": "1.0.53", "license": "BUSL-1.1", "files": ["artifacts/contracts/utils/Decoder.sol/Decoder.json", "artifacts/contracts/Relayer.sol/Relayer.json", "artifacts/contracts/UltraLightNode.sol/UltraLightNode.json", "artifacts/contracts/mocks/LayerZeroOracleMock.sol/LayerZeroOracleMock.json", "artifacts/contracts/Endpoint.sol/Endpoint.json", "deployments", "contracts/interfaces"], "scripts": {"build": "hardhat compile", "console": "hardhat console", "wireBtoA": "npx hardhat --network arbitrum-rinkeby wire-endpoints --endpoint-b bsc-testnet", "newWireBtoA": "npx hardhat --network bsc-testnet new-wire-endpoints --endpoint-b bsc-testnet --prompt false", "setDefaultAppConfig": "npx hardhat --network bsc-testnet set-default-app-config --target-network bsc-testnet", "deployEth": "npx hardhat --network eth-mainnet deploy", "deployFuji": "npx hardhat --network fuji-sandbox deploy", "deployDocsCounterMock": "npx hardhat --network fuji deploy --tags DocsCounterMock", "bsctestnet:sandbox:deploy": "hardhat --network bsctestnet-sandbox deploy", "bsctestnet:testnet:deploy": "hardhat --network bsctestnet-testnet deploy", "fuji:sandbox:deploy": "hardhat --network fuji-sandbox deploy", "fuji:testnet:deploy": "hardhat --network fuji-testnet deploy", "mumbai:sandbox:deploy": "hardhat --network mumbai-sandbox deploy", "mumbai:testnet:deploy": "hardhat --network mumbai-testnet deploy", "arbrink:sandbox:deploy": "hardhat --network arbrink-sandbox deploy", "arbrink:testnet:deploy": "hardhat --network arbrink-testnet deploy", "optkov:sandbox:deploy": "hardhat --network optkov-sandbox deploy", "optkov:testnet:deploy": "hardhat --network optkov-testnet deploy", "export": "hardhat export --export-all exports/deployments.json", "test": "hardhat test", "test:gas": "cross-env REPORT_GAS=true yarn test", "test:coverage": "cross-env NODE_OPTIONS=\"--max-old-space-size=2048\" hardhat coverage", "prettier": "prettier --write .prettierrc.js && prettier --write test/**/*.js && prettier --write test/*.js && prettier --write deploy/*.js && prettier --write tasks/*.js && prettier --write utils/*.js && prettier --write contracts/*.sol && prettier --write contracts/**/*.sol", "lint": "yarn prettier", "dev": "hardhat node"}, "devDependencies": {"@ethereumjs/common": "^2.2.0", "@layerzerolabs/lz-sdk": "0.0.10", "@layerzerolabs/prettier-plugin-solidity": "^1.0.0-beta.19", "@nomiclabs/hardhat-ethers": "^2.0.0", "@nomiclabs/hardhat-solhint": "^2.0.0", "@nomiclabs/hardhat-waffle": "^2.0.1", "@nomiclabs/hardhat-web3": "^2.0.0", "@openzeppelin/contracts": "3.4.2-solc-0.7", "@openzeppelin/contracts-upgradeable": "3.4.2-solc-0.7", "abi-decoder": "^2.4.0", "bn.js": "^5.2.0", "chai": "^4.3.4", "cli-ux": "^6.0.9", "cross-env": "^7.0.0", "dotenv": "^10.0.0", "ethereum-waffle": "^3.4.0", "ethers": "^5.4.7", "hardhat": "^2.5.0", "hardhat-deploy": "^0.9.14", "hardhat-deploy-ethers": "^0.3.0-beta.11", "hardhat-gas-reporter": "^1.0.0", "hardhat-spdx-license-identifier": "^2.0.0", "lodash": "3.8.0", "markdown-table": "^2.0.0", "mocha": "^8.3.2", "prettier": "^2.2.0", "solhint-plugin-prettier": "^0.0.5", "solidity-coverage": "^0.7.16", "ts-node": "^9.1.1", "typescript": "^4.5.4", "web3": "^1.6.1"}, "dependencies": {"@nomiclabs/hardhat-etherscan": "^3.0.3"}}
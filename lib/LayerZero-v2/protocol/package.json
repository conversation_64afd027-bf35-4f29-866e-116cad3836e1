{"name": "@layerzerolabs/lz-evm-protocol-v2", "private": true, "license": "LZBL-1.2", "scripts": {"clean": "rimraf cache out", "build": "forge build", "test": "forge test"}, "dependencies": {"@openzeppelin/contracts": "^4.8.1", "@openzeppelin/contracts-upgradeable": "^4.8.1", "solidity-bytes-utils": "^0.8.0"}, "devDependencies": {"hardhat-deploy": "^0.11.44", "rimraf": "^5.0.5"}, "publishConfig": {"access": "restricted"}, "lzVersions": {"default": ["v2"]}}
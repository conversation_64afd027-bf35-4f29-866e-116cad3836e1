[profile.default]
auto_detect_solc = true
verbosity = 3
src = "contracts"
test = "test"
out = "out"
cache_path = "cache"
optimizer = true
optimizer_runs = 20_000

allow_paths = [
    "../.yarn/unplugged",
    "../node_modules",
]

libs = [
    '../lib',
]
remappings = [
    # note: map to package level only, required for pnp-berry to work with foundry
    # ok - solidity-stringutils/=node_modules/solidity-stringutils/
    # not ok - solidity-stringutils/=node_modules/solidity-stringutils/src/
    '@openzeppelin/contracts/=node_modules/@openzeppelin/contracts/',
    '@openzeppelin/contracts-upgradeable/=node_modules/@openzeppelin/contracts-upgradeable/',
    'solidity-bytes-utils/=node_modules/solidity-bytes-utils/',
    'hardhat-deploy/=node_modules/hardhat-deploy/',
    '@layerzerolabs/lz-evm-protocol-v2/=node_modules/@layerzerolabs/lz-evm-protocol-v2/',
]
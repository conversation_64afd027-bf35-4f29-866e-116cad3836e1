{"name": "@layerzerolabs/lz-evm-oapp-v2", "private": true, "license": "MIT", "scripts": {"clean": "rimraf cache out", "build": "forge build", "test": "forge test"}, "dependencies": {"@layerzerolabs/lz-evm-messagelib-v2": "workspace:^", "@layerzerolabs/lz-evm-protocol-v2": "workspace:^", "@layerzerolabs/lz-evm-v1-0.7": "^2.0.1"}, "devDependencies": {"@openzeppelin/contracts": "^4.8.1", "@openzeppelin/contracts-upgradeable": "^4.8.1", "hardhat-deploy": "^0.11.44", "rimraf": "^5.0.5", "solidity-bytes-utils": "^0.8.0"}, "peerDependencies": {"solidity-bytes-utils": "^0.8.0"}, "publishConfig": {"access": "restricted"}, "lzVersions": {"default": ["v2"]}}
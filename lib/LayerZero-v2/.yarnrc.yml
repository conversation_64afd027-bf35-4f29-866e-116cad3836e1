compressionLevel: mixed

enableGlobalCache: false

unsafeHttpWhitelist:
  - localhost

nodeLinker: pnp

npmRegistries:
  "//localhost:4873":
    npmAuthToken: "${LOCAL_NPM_TOKEN-}"
  //registry.npmjs.org:
    npmAuthToken: "${NPM_TOKEN-}"

npmScopes:
  layerzerolabs:
    npmAlwaysAuth: true
    npmPublishRegistry: "${NPM_REGISTRY-https://registry.npmjs.org}"
    npmRegistryServer: "${NPM_REGISTRY-https://registry.npmjs.org}"

packageExtensions:
  hardhat-deploy@*:
    dependencies:
      hardhat: ^2.9.0

plugins:
  - path: .yarn/plugins/@yarnpkg/plugin-forge.js

yarnPath: .yarn/releases/yarn-4.0.2.cjs

// SPDX-License-Identifier: LZBL-1.2

pragma solidity ^0.8.20;

import { SafeCast } from "@openzeppelin/contracts/utils/math/SafeCast.sol";

import { ExecutorOptions } from "@layerzerolabs/lz-evm-protocol-v2/contracts/messagelib/libs/ExecutorOptions.sol";

import { DVNOptions } from "./DVNOptions.sol";

library UlnOptions {
    using SafeCast for uint256;

    uint16 internal constant TYPE_1 = 1; // legacy options type 1
    uint16 internal constant TYPE_2 = 2; // legacy options type 2
    uint16 internal constant TYPE_3 = 3;

    error LZ_ULN_InvalidWorkerOptions(uint256 cursor);
    error LZ_ULN_InvalidWorkerId(uint8 workerId);
    error LZ_ULN_InvalidLegacyType1Option();
    error LZ_ULN_InvalidLegacyType2Option();
    error LZ_ULN_UnsupportedOptionType(uint16 optionType);

    /// @dev decode the options into executorOptions and dvnOptions
    /// @param _options the options can be either legacy options (type 1 or 2) or type 3 options
    /// @return executorOptions the executor options, share the same format of type 3 options
    /// @return dvnOptions the dvn options, share the same format of type 3 options
    function decode(
        bytes calldata _options
    ) internal pure returns (bytes memory executorOptions, bytes memory dvnOptions) {
        // at least 2 bytes for the option type, but can have no options
        if (_options.length < 2) revert LZ_ULN_InvalidWorkerOptions(0);

        uint16 optionsType = uint16(bytes2(_options[0:2]));
        uint256 cursor = 2;

        // type3 options: [worker_option][worker_option]...
        // worker_option: [worker_id][option_size][option]
        // worker_id: uint8, option_size: uint16, option: bytes
        if (optionsType == TYPE_3) {
            unchecked {
                uint256 start = cursor;
                uint8 lastWorkerId; // worker_id starts from 1, so 0 is an invalid worker_id

                // heuristic: we assume that the options are mostly EXECUTOR options only
                // checking the workerID can reduce gas usage for most cases
                while (cursor < _options.length) {
                    uint8 workerId = uint8(bytes1(_options[cursor:cursor + 1]));
                    if (workerId == 0) revert LZ_ULN_InvalidWorkerId(0);

                    // workerId must equal to the lastWorkerId for the first option
                    // so it is always skipped in the first option
                    // this operation slices out options whenever the the scan finds a different workerId
                    if (lastWorkerId == 0) {
                        lastWorkerId = workerId;
                    } else if (workerId != lastWorkerId) {
                        bytes calldata op = _options[start:cursor]; // slice out the last worker's options
                        (executorOptions, dvnOptions) = _insertWorkerOptions(
                            executorOptions,
                            dvnOptions,
                            lastWorkerId,
                            op
                        );

                        // reset the start cursor and lastWorkerId
                        start = cursor;
                        lastWorkerId = workerId;
                    }

                    ++cursor; // for workerId

                    uint16 size = uint16(bytes2(_options[cursor:cursor + 2]));
                    if (size == 0) revert LZ_ULN_InvalidWorkerOptions(cursor);
                    cursor += size + 2;
                }

                // the options length must be the same as the cursor at the end
                if (cursor != _options.length) revert LZ_ULN_InvalidWorkerOptions(cursor);

                // if we have reached the end of the options and the options are not empty
                // we need to process the last worker's options
                if (_options.length > 2) {
                    bytes calldata op = _options[start:cursor];
                    (executorOptions, dvnOptions) = _insertWorkerOptions(executorOptions, dvnOptions, lastWorkerId, op);
                }
            }
        } else {
            executorOptions = decodeLegacyOptions(optionsType, _options);
        }
    }

    function _insertWorkerOptions(
        bytes memory _executorOptions,
        bytes memory _dvnOptions,
        uint8 _workerId,
        bytes calldata _newOptions
    ) private pure returns (bytes memory, bytes memory) {
        if (_workerId == ExecutorOptions.WORKER_ID) {
            _executorOptions = _executorOptions.length == 0
                ? _newOptions
                : abi.encodePacked(_executorOptions, _newOptions);
        } else if (_workerId == DVNOptions.WORKER_ID) {
            _dvnOptions = _dvnOptions.length == 0 ? _newOptions : abi.encodePacked(_dvnOptions, _newOptions);
        } else {
            revert LZ_ULN_InvalidWorkerId(_workerId);
        }
        return (_executorOptions, _dvnOptions);
    }

    /// @dev decode the legacy options (type 1 or 2) into executorOptions
    /// @param _optionType the legacy option type
    /// @param _options the legacy options, which still has the option type in the first 2 bytes
    /// @return executorOptions the executor options, share the same format of type 3 options
    /// Data format:
    /// legacy type 1: [extraGas]
    /// legacy type 2: [extraGas][dstNativeAmt][dstNativeAddress]
    /// extraGas: uint256, dstNativeAmt: uint256, dstNativeAddress: bytes
    function decodeLegacyOptions(
        uint16 _optionType,
        bytes calldata _options
    ) internal pure returns (bytes memory executorOptions) {
        if (_optionType == TYPE_1) {
            if (_options.length != 34) revert LZ_ULN_InvalidLegacyType1Option();

            // execution gas
            uint128 executionGas = uint256(bytes32(_options[2:2 + 32])).toUint128();

            // dont use the encode function in the ExecutorOptions lib for saving gas by calling abi.encodePacked once
            // the result is a lzReceive option: [executor_id][option_size][option_type][execution_gas]
            // option_type: uint8, execution_gas: uint128
            // option_size = len(option_type) + len(execution_gas) = 1 + 16 = 17
            executorOptions = abi.encodePacked(
                ExecutorOptions.WORKER_ID,
                uint16(17), // 16 + 1, 16 for option_length, + 1 for option_type
                ExecutorOptions.OPTION_TYPE_LZRECEIVE,
                executionGas
            );
        } else if (_optionType == TYPE_2) {
            // receiver size <= 32
            if (_options.length <= 66 || _options.length > 98) revert LZ_ULN_InvalidLegacyType2Option();

            // execution gas
            uint128 executionGas = uint256(bytes32(_options[2:2 + 32])).toUint128();

            // nativeDrop (amount + receiver)
            uint128 amount = uint256(bytes32(_options[34:34 + 32])).toUint128(); // offset 2 + 32
            bytes32 receiver;
            unchecked {
                uint256 receiverLen = _options.length - 66; // offset 2 + 32 + 32
                receiver = bytes32(_options[66:]);
                receiver = receiver >> (8 * (32 - receiverLen)); // padding 0 to the left
            }

            // dont use the encode function in the ExecutorOptions lib for saving gas by calling abi.encodePacked once
            // the result has one lzReceive option and one nativeDrop option:
            //      [executor_id][lzReceive_option_size][option_type][execution_gas] +
            //      [executor_id][nativeDrop_option_size][option_type][nativeDrop_amount][receiver]
            // option_type: uint8, execution_gas: uint128, nativeDrop_amount: uint128, receiver: bytes32
            // lzReceive_option_size = len(option_type) + len(execution_gas) = 1 + 16 = 17
            // nativeDrop_option_size = len(option_type) + len(nativeDrop_amount) + len(receiver) = 1 + 16 + 32 = 49
            executorOptions = abi.encodePacked(
                ExecutorOptions.WORKER_ID,
                uint16(17), // 16 + 1, 16 for option_length, + 1 for option_type
                ExecutorOptions.OPTION_TYPE_LZRECEIVE,
                executionGas,
                ExecutorOptions.WORKER_ID,
                uint16(49), // 48 + 1, 32 + 16 for option_length, + 1 for option_type
                ExecutorOptions.OPTION_TYPE_NATIVE_DROP,
                amount,
                receiver
            );
        } else {
            revert LZ_ULN_UnsupportedOptionType(_optionType);
        }
    }
}

# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@axelar-network/axelar-gmp-sdk-solidity@npm:^5.6.3":
  version: 5.6.4
  resolution: "@axelar-network/axelar-gmp-sdk-solidity@npm:5.6.4"
  checksum: 94443f7250fbdd046239f03fbf83489a3ee9aceeb02f76c177ae0e522a16be4b202c386bd1aaa3bd7bdc1e6c57b92d7fee8e8bd8a063f244d454a1fab8d86ddf
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0":
  version: 7.23.5
  resolution: "@babel/code-frame@npm:7.23.5"
  dependencies:
    "@babel/highlight": "npm:^7.23.4"
    chalk: "npm:^2.4.2"
  checksum: 44e58529c9d93083288dc9e649c553c5ba997475a7b0758cc3ddc4d77b8a7d985dbe78cc39c9bbc61f26d50af6da1ddf0a3427eae8cc222a9370619b671ed8f5
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/compat-data@npm:7.23.5"
  checksum: 088f14f646ecbddd5ef89f120a60a1b3389a50a9705d44603dca77662707d0175a5e0e0da3943c3298f1907a4ab871468656fbbf74bb7842cd8b0686b2c19736
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.6":
  version: 7.23.6
  resolution: "@babel/helper-compilation-targets@npm:7.23.6"
  dependencies:
    "@babel/compat-data": "npm:^7.23.5"
    "@babel/helper-validator-option": "npm:^7.23.5"
    browserslist: "npm:^4.22.2"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 05595cd73087ddcd81b82d2f3297aac0c0422858dfdded43d304786cf680ec33e846e2317e6992d2c964ee61d93945cbf1fa8ec80b55aee5bfb159227fb02cb9
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.4.4":
  version: 0.4.4
  resolution: "@babel/helper-define-polyfill-provider@npm:0.4.4"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 16c312e40ecf2ead81f3ab7275387079071012d2363022c04cf16d56fe0d781185f3a517b928f4556c716ae45e0567b817b636d5cd2fee8fb2ce2b18a04c5bcd
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/helper-module-imports@npm:7.22.15"
  dependencies:
    "@babel/types": "npm:^7.22.15"
  checksum: 5ecf9345a73b80c28677cfbe674b9f567bb0d079e37dcba9055e36cb337db24ae71992a58e1affa9d14a60d3c69907d30fe1f80aea105184501750a58d15c81c
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-plugin-utils@npm:7.22.5"
  checksum: ab220db218089a2aadd0582f5833fd17fa300245999f5f8784b10f5a75267c4e808592284a29438a0da365e702f05acb369f99e1c915c02f9f9210ec60eab8ea
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/helper-string-parser@npm:7.23.4"
  checksum: c352082474a2ee1d2b812bd116a56b2e8b38065df9678a32a535f151ec6f58e54633cc778778374f10544b930703cca6ddf998803888a636afa27e2658068a9c
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-validator-identifier@npm:7.22.20"
  checksum: df882d2675101df2d507b95b195ca2f86a3ef28cb711c84f37e79ca23178e13b9f0d8b522774211f51e40168bf5142be4c1c9776a150cddb61a0d5bf3e95750b
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/helper-validator-option@npm:7.23.5"
  checksum: 537cde2330a8aede223552510e8a13e9c1c8798afee3757995a7d4acae564124fe2bf7e7c3d90d62d3657434a74340a274b3b3b1c6f17e9a2be1f48af29cb09e
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/highlight@npm:7.23.4"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.22.20"
    chalk: "npm:^2.4.2"
    js-tokens: "npm:^4.0.0"
  checksum: 62fef9b5bcea7131df4626d009029b1ae85332042f4648a4ce6e740c3fd23112603c740c45575caec62f260c96b11054d3be5987f4981a5479793579c3aac71f
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.5.5":
  version: 7.23.6
  resolution: "@babel/plugin-transform-runtime@npm:7.23.6"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    babel-plugin-polyfill-corejs2: "npm:^0.4.6"
    babel-plugin-polyfill-corejs3: "npm:^0.8.5"
    babel-plugin-polyfill-regenerator: "npm:^0.5.3"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 54e540ec04f05f664c69db36a78b5db56b0d3e25bbe34d7f11cfe21cc5aad5240661f5ada630c7b2abcae99d229851aafbb6b2218cf285771379e0844a3f24a9
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.5.5":
  version: 7.23.6
  resolution: "@babel/runtime@npm:7.23.6"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 4c4ab16f0361c59fb23956e4d0a29935f1f8a64aa8dd37876ce38355b6f4d8f0e54237aacb89c73b1532def60539ddde2d651523c8fa887b30b19a8cf0c465b0
  languageName: node
  linkType: hard

"@babel/types@npm:^7.22.15":
  version: 7.23.6
  resolution: "@babel/types@npm:7.23.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.23.4"
    "@babel/helper-validator-identifier": "npm:^7.22.20"
    to-fast-properties: "npm:^2.0.0"
  checksum: 07e70bb94d30b0231396b5e9a7726e6d9227a0a62e0a6830c0bd3232f33b024092e3d5a7d1b096a65bbf2bb43a9ab4c721bf618e115bfbb87b454fa060f88cbf
  languageName: node
  linkType: hard

"@chainlink/contracts-ccip@npm:^0.7.6":
  version: 0.7.6
  resolution: "@chainlink/contracts-ccip@npm:0.7.6"
  dependencies:
    "@eth-optimism/contracts": "npm:^0.5.21"
    "@openzeppelin/contracts": "npm:~4.3.3"
    "@openzeppelin/contracts-upgradeable-4.7.3": "npm:@openzeppelin/contracts-upgradeable@v4.7.3"
    "@openzeppelin/contracts-v0.7": "npm:@openzeppelin/contracts@v3.4.2"
  checksum: 1a2daf828c72ad0ff192d3c9c0d8517be17fc4e6c44db5d90a4f40a7a7104844d184ad2cc0873acdaee7b8cb020d343dadd1d9cb883de90c1f60ec37bcc68d34
  languageName: node
  linkType: hard

"@chainsafe/as-sha256@npm:^0.3.1":
  version: 0.3.1
  resolution: "@chainsafe/as-sha256@npm:0.3.1"
  checksum: 3bae7b4bc6e307baa3cf1f9d2c75827874cd0fb458bc592656d741d374b48e71c042fe21616a506cb821487a5abfc6b92181e4b7fbf49b7370cee4df0b67d95a
  languageName: node
  linkType: hard

"@chainsafe/persistent-merkle-tree@npm:^0.4.2":
  version: 0.4.2
  resolution: "@chainsafe/persistent-merkle-tree@npm:0.4.2"
  dependencies:
    "@chainsafe/as-sha256": "npm:^0.3.1"
  checksum: a7e59f80be3ce0a86fe452a3c003bd159a1719ed22cae22e9841668f0eda8c35412fa16b3b150d96f583a24f430a5cc2a1bfcabafc1b9cf6e1fdb227e98c4dc7
  languageName: node
  linkType: hard

"@chainsafe/persistent-merkle-tree@npm:^0.5.0":
  version: 0.5.0
  resolution: "@chainsafe/persistent-merkle-tree@npm:0.5.0"
  dependencies:
    "@chainsafe/as-sha256": "npm:^0.3.1"
  checksum: c8a37eb2fbe04d8b6f219774400dad5c50e109a9daf427883c9e33826a294a1bbd6bc759b5d6d38fefb2398443d2d880b67130eacab55b34d95d1332ac8ab680
  languageName: node
  linkType: hard

"@chainsafe/ssz@npm:^0.10.0":
  version: 0.10.2
  resolution: "@chainsafe/ssz@npm:0.10.2"
  dependencies:
    "@chainsafe/as-sha256": "npm:^0.3.1"
    "@chainsafe/persistent-merkle-tree": "npm:^0.5.0"
  checksum: 359b3a672b460ad7fee524115fe7e5d9518c62b667dfc3dc6d8be0286ebb785ce303a68070cde5b31fc2860f99fda40df4296030cb9af42554143290f542326b
  languageName: node
  linkType: hard

"@chainsafe/ssz@npm:^0.9.2":
  version: 0.9.4
  resolution: "@chainsafe/ssz@npm:0.9.4"
  dependencies:
    "@chainsafe/as-sha256": "npm:^0.3.1"
    "@chainsafe/persistent-merkle-tree": "npm:^0.4.2"
    case: "npm:^1.6.3"
  checksum: 2fe83d0b3ef131e14b51b88bb3343b14e7a02185fa9fd3da84b4726dbd857daaa4f7f6f4840fe3772fc1380352b1675a13b5f6153c4211c0f00ffa542b62bf2f
  languageName: node
  linkType: hard

"@eth-optimism/contracts@npm:^0.5.21":
  version: 0.5.40
  resolution: "@eth-optimism/contracts@npm:0.5.40"
  dependencies:
    "@eth-optimism/core-utils": "npm:0.12.0"
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
  peerDependencies:
    ethers: ^5
  checksum: 5333f7d5792ad7c656202ce6e9e875993da7423b670f3da5e1cee9330d6c29b4f102ecc2fee06ca353381a2e603485e8a883e8a3979218effec2ead84f46e9a2
  languageName: node
  linkType: hard

"@eth-optimism/core-utils@npm:0.12.0":
  version: 0.12.0
  resolution: "@eth-optimism/core-utils@npm:0.12.0"
  dependencies:
    "@ethersproject/abi": "npm:^5.7.0"
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/contracts": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/providers": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/web": "npm:^5.7.0"
    bufio: "npm:^1.0.7"
    chai: "npm:^4.3.4"
  checksum: a7ea17a8b529b2c86b00ef19fa562c2b792d7e8a4071defea4d8a8b82a101105a3ab6dc86361118e17bf9b4784b4eca9c1e937c8b1e7294a1a850f97b5a73a10
  languageName: node
  linkType: hard

"@ethereumjs/common@npm:2.5.0":
  version: 2.5.0
  resolution: "@ethereumjs/common@npm:2.5.0"
  dependencies:
    crc-32: "npm:^1.2.0"
    ethereumjs-util: "npm:^7.1.1"
  checksum: 8fba3e67b184f040d0f7630be84315d2738e3e322fc3559e00a5083b790e85433d5ea6b98833bc802eb2c8569f97a0166ffb2a592eeb09e942c0de9262a8c346
  languageName: node
  linkType: hard

"@ethereumjs/common@npm:^2.4.0, @ethereumjs/common@npm:^2.5.0, @ethereumjs/common@npm:^2.6.4":
  version: 2.6.5
  resolution: "@ethereumjs/common@npm:2.6.5"
  dependencies:
    crc-32: "npm:^1.2.0"
    ethereumjs-util: "npm:^7.1.5"
  checksum: e931e16cafc908b086492ca5fcbb1820fff3edfb83cfd4ae48002517b3be0d1f7622c750874b3b347c122d06372e133ddae44ac129b5ba141f68808a79430135
  languageName: node
  linkType: hard

"@ethereumjs/tx@npm:3.3.2":
  version: 3.3.2
  resolution: "@ethereumjs/tx@npm:3.3.2"
  dependencies:
    "@ethereumjs/common": "npm:^2.5.0"
    ethereumjs-util: "npm:^7.1.2"
  checksum: 9d88b9627b9c6a465b931ddba7a6aa4a124d3662c91f5f6658be1ad11fefd7f7429042feb47f5676854300a8cb3e107184154d1123d7292a92938afc057b58de
  languageName: node
  linkType: hard

"@ethereumjs/tx@npm:^3.3.0":
  version: 3.5.2
  resolution: "@ethereumjs/tx@npm:3.5.2"
  dependencies:
    "@ethereumjs/common": "npm:^2.6.4"
    ethereumjs-util: "npm:^7.1.5"
  checksum: 891e12738206229ac428685536844f7765e8547ae794462b1e406399445bf1f6f918af6ebc33ee5fa4a1340f14f48871a579f11c0e1d7c142ba0dd525bae5df5
  languageName: node
  linkType: hard

"@ethersproject/abi@npm:5.7.0, @ethersproject/abi@npm:^5.1.2, @ethersproject/abi@npm:^5.6.3, @ethersproject/abi@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abi@npm:5.7.0"
  dependencies:
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 6ed002cbc61a7e21bc0182702345659c1984f6f8e6bad166e43aee76ea8f74766dd0f6236574a868e1b4600af27972bf25b973fae7877ae8da3afa90d3965cac
  languageName: node
  linkType: hard

"@ethersproject/abstract-provider@npm:5.7.0, @ethersproject/abstract-provider@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abstract-provider@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/networks": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/web": "npm:^5.7.0"
  checksum: c03e413a812486002525f4036bf2cb90e77a19b98fa3d16279e28e0a05520a1085690fac2ee9f94b7931b9a803249ff8a8bbb26ff8dee52196a6ef7a3fc5edc5
  languageName: node
  linkType: hard

"@ethersproject/abstract-signer@npm:5.7.0, @ethersproject/abstract-signer@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/abstract-signer@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
  checksum: 0a6ffade0a947c9ba617048334e1346838f394d1d0a5307ac435a0c63ed1033b247e25ffb0cd6880d7dcf5459581f52f67e3804ebba42ff462050f1e4321ba0c
  languageName: node
  linkType: hard

"@ethersproject/address@npm:5.7.0, @ethersproject/address@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/address@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
  checksum: 1ac4f3693622ed9fbbd7e966a941ec1eba0d9445e6e8154b1daf8e93b8f62ad91853d1de5facf4c27b41e6f1e47b94a317a2492ba595bee1841fd3030c3e9a27
  languageName: node
  linkType: hard

"@ethersproject/base64@npm:5.7.0, @ethersproject/base64@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/base64@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
  checksum: 7105105f401e1c681e61db1e9da1b5960d8c5fbd262bbcacc99d61dbb9674a9db1181bb31903d98609f10e8a0eb64c850475f3b040d67dea953e2b0ac6380e96
  languageName: node
  linkType: hard

"@ethersproject/basex@npm:5.7.0, @ethersproject/basex@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/basex@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
  checksum: 840e333e109bff2fcf8d91dcfd45fa951835844ef0e1ba710037e87291c7b5f3c189ba86f6cee2ca7de2ede5b7d59fbb930346607695855bee20d2f9f63371ef
  languageName: node
  linkType: hard

"@ethersproject/bignumber@npm:5.7.0, @ethersproject/bignumber@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/bignumber@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    bn.js: "npm:^5.2.1"
  checksum: 09cffa18a9f0730856b57c14c345bd68ba451159417e5aff684a8808011cd03b27b7c465d423370333a7d1c9a621392fc74f064a3b02c9edc49ebe497da6d45d
  languageName: node
  linkType: hard

"@ethersproject/bytes@npm:5.7.0, @ethersproject/bytes@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/bytes@npm:5.7.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 8b3ffedb68c1a82cfb875e9738361409cc33e2dcb1286b6ccfdc4dd8dd0317f7eacc8937b736c467d213dffc44b469690fe1a951e901953d5a90c5af2b675ae4
  languageName: node
  linkType: hard

"@ethersproject/constants@npm:5.7.0, @ethersproject/constants@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/constants@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
  checksum: 6d4b1355747cce837b3e76ec3bde70e4732736f23b04f196f706ebfa5d4d9c2be50904a390d4d40ce77803b98d03d16a9b6898418e04ba63491933ce08c4ba8a
  languageName: node
  linkType: hard

"@ethersproject/contracts@npm:5.7.0, @ethersproject/contracts@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/contracts@npm:5.7.0"
  dependencies:
    "@ethersproject/abi": "npm:^5.7.0"
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
  checksum: 5df66179af242faabea287a83fd2f8f303a4244dc87a6ff802e1e3b643f091451295c8e3d088c7739970b7915a16a581c192d4e007d848f1fdf3cc9e49010053
  languageName: node
  linkType: hard

"@ethersproject/hash@npm:5.7.0, @ethersproject/hash@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/hash@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/base64": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: d83de3f3a1b99b404a2e7bb503f5cdd90c66a97a32cce1d36b09bb8e3fb7205b96e30ad28e2b9f30083beea6269b157d0c6e3425052bb17c0a35fddfdd1c72a3
  languageName: node
  linkType: hard

"@ethersproject/hdnode@npm:5.7.0, @ethersproject/hdnode@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/hdnode@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/basex": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/pbkdf2": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/sha2": "npm:^5.7.0"
    "@ethersproject/signing-key": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/wordlists": "npm:^5.7.0"
  checksum: 2fbe6278c324235afaa88baa5dea24d8674c72b14ad037fe2096134d41025977f410b04fd146e333a1b6cac9482e9de62d6375d1705fd42667543f2d0eb66655
  languageName: node
  linkType: hard

"@ethersproject/json-wallets@npm:5.7.0, @ethersproject/json-wallets@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/json-wallets@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/hdnode": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/pbkdf2": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/random": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    aes-js: "npm:3.0.0"
    scrypt-js: "npm:3.0.1"
  checksum: 4a1ef0912ffc8d18c392ae4e292948d86bffd715fe3dd3e66d1cd21f6c9267aeadad4da84261db853327f97cdfd765a377f9a87e39d4c6749223a69226faf0a1
  languageName: node
  linkType: hard

"@ethersproject/keccak256@npm:5.7.0, @ethersproject/keccak256@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/keccak256@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    js-sha3: "npm:0.8.0"
  checksum: ff70950d82203aab29ccda2553422cbac2e7a0c15c986bd20a69b13606ed8bb6e4fdd7b67b8d3b27d4f841e8222cbaccd33ed34be29f866fec7308f96ed244c6
  languageName: node
  linkType: hard

"@ethersproject/logger@npm:5.7.0, @ethersproject/logger@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/logger@npm:5.7.0"
  checksum: 683a939f467ae7510deedc23d7611d0932c3046137f5ffb92ba1e3c8cd9cf2fbbaa676b660c248441a0fa9143783137c46d6e6d17d676188dd5a6ef0b72dd091
  languageName: node
  linkType: hard

"@ethersproject/networks@npm:5.7.1, @ethersproject/networks@npm:^5.7.0":
  version: 5.7.1
  resolution: "@ethersproject/networks@npm:5.7.1"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 5265d0b4b72ef91af57be804b44507f4943038d609699764d8a69157ed381e30fe22ebf63630ed8e530ceb220f15d69dae8cda2e5023ccd793285c9d5882e599
  languageName: node
  linkType: hard

"@ethersproject/pbkdf2@npm:5.7.0, @ethersproject/pbkdf2@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/pbkdf2@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/sha2": "npm:^5.7.0"
  checksum: dea7ba747805e24b81dfb99e695eb329509bf5cad1a42e48475ade28e060e567458a3d5bf930f302691bded733fd3fa364f0c7adce920f9f05a5ef8c13267aaa
  languageName: node
  linkType: hard

"@ethersproject/properties@npm:5.7.0, @ethersproject/properties@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/properties@npm:5.7.0"
  dependencies:
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: f8401a161940aa1c32695115a20c65357877002a6f7dc13ab1600064bf54d7b825b4db49de8dc8da69efcbb0c9f34f8813e1540427e63e262ab841c1bf6c1c1e
  languageName: node
  linkType: hard

"@ethersproject/providers@npm:5.7.2, @ethersproject/providers@npm:^5.7.0, @ethersproject/providers@npm:^5.7.1, @ethersproject/providers@npm:^5.7.2":
  version: 5.7.2
  resolution: "@ethersproject/providers@npm:5.7.2"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/base64": "npm:^5.7.0"
    "@ethersproject/basex": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/networks": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/random": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
    "@ethersproject/sha2": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/web": "npm:^5.7.0"
    bech32: "npm:1.1.4"
    ws: "npm:7.4.6"
  checksum: 8534a1896e61b9f0b66427a639df64a5fe76d0c08ec59b9f0cc64fdd1d0cc28d9fc3312838ae8d7817c8f5e2e76b7f228b689bc33d1cbb8e1b9517d4c4f678d8
  languageName: node
  linkType: hard

"@ethersproject/random@npm:5.7.0, @ethersproject/random@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/random@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: c23ec447998ce1147651bd58816db4d12dbeb404f66a03d14a13e1edb439879bab18528e1fc46b931502903ac7b1c08ea61d6a86e621a6e060fa63d41aeed3ac
  languageName: node
  linkType: hard

"@ethersproject/rlp@npm:5.7.0, @ethersproject/rlp@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/rlp@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 3b8c5279f7654794d5874569f5598ae6a880e19e6616013a31e26c35c5f586851593a6e85c05ed7b391fbc74a1ea8612dd4d867daefe701bf4e8fcf2ab2f29b9
  languageName: node
  linkType: hard

"@ethersproject/sha2@npm:5.7.0, @ethersproject/sha2@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/sha2@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    hash.js: "npm:1.1.7"
  checksum: 09321057c022effbff4cc2d9b9558228690b5dd916329d75c4b1ffe32ba3d24b480a367a7cc92d0f0c0b1c896814d03351ae4630e2f1f7160be2bcfbde435dbc
  languageName: node
  linkType: hard

"@ethersproject/signing-key@npm:5.7.0, @ethersproject/signing-key@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/signing-key@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    bn.js: "npm:^5.2.1"
    elliptic: "npm:6.5.4"
    hash.js: "npm:1.1.7"
  checksum: ff2f79ded86232b139e7538e4aaa294c6022a7aaa8c95a6379dd7b7c10a6d363685c6967c816f98f609581cf01f0a5943c667af89a154a00bcfe093a8c7f3ce7
  languageName: node
  linkType: hard

"@ethersproject/solidity@npm:5.7.0, @ethersproject/solidity@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/solidity@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/sha2": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 9a02f37f801c96068c3e7721f83719d060175bc4e80439fe060e92bd7acfcb6ac1330c7e71c49f4c2535ca1308f2acdcb01e00133129aac00581724c2d6293f3
  languageName: node
  linkType: hard

"@ethersproject/strings@npm:5.7.0, @ethersproject/strings@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/strings@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 24191bf30e98d434a9fba2f522784f65162d6712bc3e1ccc98ed85c5da5884cfdb5a1376b7695374655a7b95ec1f5fdbeef5afc7d0ea77ffeb78047e9b791fa5
  languageName: node
  linkType: hard

"@ethersproject/transactions@npm:5.7.0, @ethersproject/transactions@npm:^5.6.2, @ethersproject/transactions@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/transactions@npm:5.7.0"
  dependencies:
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/rlp": "npm:^5.7.0"
    "@ethersproject/signing-key": "npm:^5.7.0"
  checksum: d809e9d40020004b7de9e34bf39c50377dce8ed417cdf001bfabc81ecb1b7d1e0c808fdca0a339ea05e1b380648eaf336fe70f137904df2d3c3135a38190a5af
  languageName: node
  linkType: hard

"@ethersproject/units@npm:5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/units@npm:5.7.0"
  dependencies:
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
  checksum: 304714f848cd32e57df31bf545f7ad35c2a72adae957198b28cbc62166daa929322a07bff6e9c9ac4577ab6aa0de0546b065ed1b2d20b19e25748b7d475cb0fc
  languageName: node
  linkType: hard

"@ethersproject/wallet@npm:5.7.0, @ethersproject/wallet@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/wallet@npm:5.7.0"
  dependencies:
    "@ethersproject/abstract-provider": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/hdnode": "npm:^5.7.0"
    "@ethersproject/json-wallets": "npm:^5.7.0"
    "@ethersproject/keccak256": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/random": "npm:^5.7.0"
    "@ethersproject/signing-key": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/wordlists": "npm:^5.7.0"
  checksum: 340f8e5c77c6c47c4d1596c200d97c53c1d4b4eb54d9166d0f2a114cb81685e7689255b0627e917fbcdc29cb54c4bd1f1a9909f3096ef9dff9acc0b24972f1c1
  languageName: node
  linkType: hard

"@ethersproject/web@npm:5.7.1, @ethersproject/web@npm:^5.7.0":
  version: 5.7.1
  resolution: "@ethersproject/web@npm:5.7.1"
  dependencies:
    "@ethersproject/base64": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: c83b6b3ac40573ddb67b1750bb4cf21ded7d8555be5e53a97c0f34964622fd88de9220a90a118434bae164a2bff3acbdc5ecb990517b5f6dc32bdad7adf604c2
  languageName: node
  linkType: hard

"@ethersproject/wordlists@npm:5.7.0, @ethersproject/wordlists@npm:^5.7.0":
  version: 5.7.0
  resolution: "@ethersproject/wordlists@npm:5.7.0"
  dependencies:
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/hash": "npm:^5.7.0"
    "@ethersproject/logger": "npm:^5.7.0"
    "@ethersproject/properties": "npm:^5.7.0"
    "@ethersproject/strings": "npm:^5.7.0"
  checksum: 737fca67ad743a32020f50f5b9e147e5683cfba2692367c1124a5a5538be78515865257b426ec9141daac91a70295e5e21bef7a193b79fe745f1be378562ccaa
  languageName: node
  linkType: hard

"@fastify/busboy@npm:^2.0.0":
  version: 2.1.0
  resolution: "@fastify/busboy@npm:2.1.0"
  checksum: f22c1e5c52dc350ddf9ba8be9f87b48d3ea5af00a37fd0a0d1e3e4b37f94d96763e514c68a350c7f570260fdd2f08b55ee090cdd879f92a03249eb0e3fd19113
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@layerzerolabs/layerzero-v2@workspace:.":
  version: 0.0.0-use.local
  resolution: "@layerzerolabs/layerzero-v2@workspace:."
  dependencies:
    "@layerzerolabs/prettier-config-next": "npm:^2.0.1"
    "@layerzerolabs/solhint-config": "npm:^2.0.1"
    prettier: "npm:^3.1.0"
    rimraf: "npm:^5.0.5"
    solhint: "npm:^4.0.0"
  dependenciesMeta:
    "@axelar-network/axelar-gmp-sdk-solidity":
      unplugged: true
    "@chainlink/contracts-ccip":
      unplugged: true
    "@layerzerolabs/lz-evm-v1-0.7":
      unplugged: true
    "@layerzerolabs/solidity-examples":
      unplugged: true
    "@openzeppelin/contracts":
      unplugged: true
    "@openzeppelin/contracts-upgradeable":
      unplugged: true
    hardhat-deploy:
      unplugged: true
    solidity-bytes-utils:
      unplugged: true
    solidity-stringutils:
      unplugged: true
  languageName: unknown
  linkType: soft

"@layerzerolabs/lz-evm-messagelib-v2@workspace:^, @layerzerolabs/lz-evm-messagelib-v2@workspace:messagelib":
  version: 0.0.0-use.local
  resolution: "@layerzerolabs/lz-evm-messagelib-v2@workspace:messagelib"
  dependencies:
    "@axelar-network/axelar-gmp-sdk-solidity": "npm:^5.6.3"
    "@chainlink/contracts-ccip": "npm:^0.7.6"
    "@layerzerolabs/lz-evm-protocol-v2": "workspace:^"
    "@layerzerolabs/lz-evm-v1-0.7": "npm:^2.0.1"
    "@openzeppelin/contracts": "npm:^4.8.1"
    "@openzeppelin/contracts-upgradeable": "npm:^4.8.1"
    hardhat-deploy: "npm:^0.11.44"
    rimraf: "npm:^5.0.5"
    solidity-bytes-utils: "npm:^0.8.0"
  languageName: unknown
  linkType: soft

"@layerzerolabs/lz-evm-oapp-v2@workspace:oapp":
  version: 0.0.0-use.local
  resolution: "@layerzerolabs/lz-evm-oapp-v2@workspace:oapp"
  dependencies:
    "@layerzerolabs/lz-evm-messagelib-v2": "workspace:^"
    "@layerzerolabs/lz-evm-protocol-v2": "workspace:^"
    "@layerzerolabs/lz-evm-v1-0.7": "npm:^2.0.1"
    "@openzeppelin/contracts": "npm:^4.8.1"
    "@openzeppelin/contracts-upgradeable": "npm:^4.8.1"
    hardhat-deploy: "npm:^0.11.44"
    rimraf: "npm:^5.0.5"
    solidity-bytes-utils: "npm:^0.8.0"
  peerDependencies:
    solidity-bytes-utils: ^0.8.0
  languageName: unknown
  linkType: soft

"@layerzerolabs/lz-evm-protocol-v2@workspace:^, @layerzerolabs/lz-evm-protocol-v2@workspace:protocol":
  version: 0.0.0-use.local
  resolution: "@layerzerolabs/lz-evm-protocol-v2@workspace:protocol"
  dependencies:
    "@openzeppelin/contracts": "npm:^4.8.1"
    "@openzeppelin/contracts-upgradeable": "npm:^4.8.1"
    hardhat-deploy: "npm:^0.11.44"
    rimraf: "npm:^5.0.5"
    solidity-bytes-utils: "npm:^0.8.0"
  languageName: unknown
  linkType: soft

"@layerzerolabs/lz-evm-v1-0.7@npm:^2.0.1":
  version: 2.0.1
  resolution: "@layerzerolabs/lz-evm-v1-0.7@npm:2.0.1"
  checksum: 2998e9fc1b10238dfe171651849d8ed246f57b1b6c10ad5f68d9061084796651a03fc9b8631fa74f8b1162cd94834f50cd997a8b7727b909d20596bff0c835ab
  languageName: node
  linkType: hard

"@layerzerolabs/prettier-config-next@npm:^2.0.1":
  version: 2.0.1
  resolution: "@layerzerolabs/prettier-config-next@npm:2.0.1"
  dependencies:
    prettier: "npm:^3.1.0"
    prettier-plugin-packagejson: "npm:^2.4.7"
    prettier-plugin-solidity: "npm:^1.2.0"
  checksum: f51e706ac7509b2f62ffe5864f1a0ce8b0de2db1e83bcd7c1f03f3a96447a9408e8af96130a77dd229a2951de5472034cd6ae680e3fd7cdae2096f8a8d42052a
  languageName: node
  linkType: hard

"@layerzerolabs/solhint-config@npm:^2.0.1":
  version: 2.0.1
  resolution: "@layerzerolabs/solhint-config@npm:2.0.1"
  dependencies:
    solhint: "npm:^4.0.0"
  checksum: 2890c072ae8c5a2301b1c6ea3b77b60466e3288f822dc53ff580b321c387094c81429e35564d88e6597f473e87974becc2ce68859b0859216c8dcf44a105b606
  languageName: node
  linkType: hard

"@metamask/eth-sig-util@npm:4.0.1, @metamask/eth-sig-util@npm:^4.0.0":
  version: 4.0.1
  resolution: "@metamask/eth-sig-util@npm:4.0.1"
  dependencies:
    ethereumjs-abi: "npm:^0.6.8"
    ethereumjs-util: "npm:^6.2.1"
    ethjs-util: "npm:^0.1.6"
    tweetnacl: "npm:^1.0.3"
    tweetnacl-util: "npm:^0.15.1"
  checksum: a41a986abd14675badeb02041466e30e1c3ef529c1d131f47c27fd48d73144fcf590f45d8ee8b7cd357725ebf75ece93f4484adf1baf6311cc996f7ef82c4ae1
  languageName: node
  linkType: hard

"@metamask/safe-event-emitter@npm:^2.0.0":
  version: 2.0.0
  resolution: "@metamask/safe-event-emitter@npm:2.0.0"
  checksum: 3e4f00c64aa1ddf9b9ae5c2337fb8cee359b6c481ded0ec21ef70610960c51cdcc4a9b569de334dcd7cb1fe445cafd298360907c1e211e244c5990b55246f350
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.1.2":
  version: 1.1.2
  resolution: "@noble/hashes@npm:1.1.2"
  checksum: 2826c94ea30b8d2447fda549f4ffa97a637a480eeef5c96702a2f932c305038465f7436caf5b2bad41eb43c08c270b921e101488b18165feebe3854091b56d91
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.2.0, @noble/hashes@npm:~1.2.0":
  version: 1.2.0
  resolution: "@noble/hashes@npm:1.2.0"
  checksum: c295684a2799f4ddad10a855efd9b82c70c27ac5f7437642df9700e120087c796851dd95b12d2e7596802303fe6afbfdf0f8733b5c7453f70c4c080746dde6ff
  languageName: node
  linkType: hard

"@noble/hashes@npm:~1.1.1":
  version: 1.1.5
  resolution: "@noble/hashes@npm:1.1.5"
  checksum: 21dba8e059927d9d50772660e2e50f1c0b1dd9699958fcf337d04d795dd37ddf42fe5c815e32c3ec77600b1d055c287ba10e3d24902d788bd7289851cce3260b
  languageName: node
  linkType: hard

"@noble/secp256k1@npm:1.6.3, @noble/secp256k1@npm:~1.6.0":
  version: 1.6.3
  resolution: "@noble/secp256k1@npm:1.6.3"
  checksum: e4f4b0cfa1c5d23fb1b9938fa3cce1a1160a76a89eb91f6dde98075bbdf328709d51771c85b6b4b118f8ce5a6c6554da6c9af7de7716aba56cef30f61a715bd7
  languageName: node
  linkType: hard

"@noble/secp256k1@npm:1.7.1, @noble/secp256k1@npm:~1.7.0":
  version: 1.7.1
  resolution: "@noble/secp256k1@npm:1.7.1"
  checksum: 214d4756c20ed20809d948d0cc161e95664198cb127266faf747fd7deffe5444901f05fe9f833787738f2c6e60b09e544c2f737f42f73b3699e3999ba15b1b63
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-block@npm:5.0.2":
  version: 5.0.2
  resolution: "@nomicfoundation/ethereumjs-block@npm:5.0.2"
  dependencies:
    "@nomicfoundation/ethereumjs-common": "npm:4.0.2"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-trie": "npm:6.0.2"
    "@nomicfoundation/ethereumjs-tx": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    ethereum-cryptography: "npm:0.1.3"
    ethers: "npm:^5.7.1"
  checksum: e3d7c24aa10306ae26389ce464f71e36fe8d331706e942a626594afdd097451d3bda210238b1da843e582b347f4349115b53432076b0f9b37ba36e31818b12cb
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-blockchain@npm:7.0.2":
  version: 7.0.2
  resolution: "@nomicfoundation/ethereumjs-blockchain@npm:7.0.2"
  dependencies:
    "@nomicfoundation/ethereumjs-block": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-common": "npm:4.0.2"
    "@nomicfoundation/ethereumjs-ethash": "npm:3.0.2"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-trie": "npm:6.0.2"
    "@nomicfoundation/ethereumjs-tx": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    abstract-level: "npm:^1.0.3"
    debug: "npm:^4.3.3"
    ethereum-cryptography: "npm:0.1.3"
    level: "npm:^8.0.0"
    lru-cache: "npm:^5.1.1"
    memory-level: "npm:^1.0.0"
  checksum: 4cc27cf1d39175f93ec02b4627f0320258685fc19541c0cb2b9208204e65a924f3fbeb69c23840d7100a75c6d88b7a0c65f552cb44bf1e06b530a39ac6f04e17
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-common@npm:4.0.2":
  version: 4.0.2
  resolution: "@nomicfoundation/ethereumjs-common@npm:4.0.2"
  dependencies:
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    crc-32: "npm:^1.2.0"
  checksum: ea0199240a9cfc932450421b00ba390a9d357092fdbb6ceee2b09ee6a8bc7820fb1df076ead94e0b6748158620ec655099e401816d47ad24eb603694f3a787d1
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-ethash@npm:3.0.2":
  version: 3.0.2
  resolution: "@nomicfoundation/ethereumjs-ethash@npm:3.0.2"
  dependencies:
    "@nomicfoundation/ethereumjs-block": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    abstract-level: "npm:^1.0.3"
    bigint-crypto-utils: "npm:^3.0.23"
    ethereum-cryptography: "npm:0.1.3"
  checksum: c7d963a6806e70cb96ff290a9bc461fb6ad4f74144d9f38eb5f190b228d9b0961aa67d398f80da4b59efb8c57957c936b32faab26c17cf07a6145efe0a6d16e9
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-evm@npm:2.0.2":
  version: 2.0.2
  resolution: "@nomicfoundation/ethereumjs-evm@npm:2.0.2"
  dependencies:
    "@ethersproject/providers": "npm:^5.7.1"
    "@nomicfoundation/ethereumjs-common": "npm:4.0.2"
    "@nomicfoundation/ethereumjs-tx": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    debug: "npm:^4.3.3"
    ethereum-cryptography: "npm:0.1.3"
    mcl-wasm: "npm:^0.7.1"
    rustbn.js: "npm:~0.2.0"
  checksum: abcc280500d776a214b282d5a66093fdf3b91582e9f69d6f35e0c7000f58af57060cd384acda7d147193f962f17a8c4ed55e5f2453b2d5b43d694d6588306ef9
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-rlp@npm:5.0.2":
  version: 5.0.2
  resolution: "@nomicfoundation/ethereumjs-rlp@npm:5.0.2"
  bin:
    rlp: bin/rlp
  checksum: ceb820296624f45fa8f7cd3e2cfa4d229722953e91631e6fb3fbca9d1ebe8eea21a8ef4917fa15295f05bed369db06eb81f44e74382b1bca4ece2bb000c6e6d4
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-statemanager@npm:2.0.2":
  version: 2.0.2
  resolution: "@nomicfoundation/ethereumjs-statemanager@npm:2.0.2"
  dependencies:
    "@nomicfoundation/ethereumjs-common": "npm:4.0.2"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    debug: "npm:^4.3.3"
    ethereum-cryptography: "npm:0.1.3"
    ethers: "npm:^5.7.1"
    js-sdsl: "npm:^4.1.4"
  checksum: 0eb939c75aa63517ec8832330c02a71c060fd8424ebe608a072c563a0a9d16621dca9a9c851fa98971316aed80dd9174a0d72ae09633a9698367a3316d773389
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-trie@npm:6.0.2":
  version: 6.0.2
  resolution: "@nomicfoundation/ethereumjs-trie@npm:6.0.2"
  dependencies:
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    "@types/readable-stream": "npm:^2.3.13"
    ethereum-cryptography: "npm:0.1.3"
    readable-stream: "npm:^3.6.0"
  checksum: f70b89e7f089a90647ea96babc23427cf4db3bdc027667b93be38a0ff03f3222c5130529778fb210ffa56eb2a53cb1657cafdfe828c6a41825bb7e2b74614060
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-tx@npm:5.0.2":
  version: 5.0.2
  resolution: "@nomicfoundation/ethereumjs-tx@npm:5.0.2"
  dependencies:
    "@chainsafe/ssz": "npm:^0.9.2"
    "@ethersproject/providers": "npm:^5.7.2"
    "@nomicfoundation/ethereumjs-common": "npm:4.0.2"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    ethereum-cryptography: "npm:0.1.3"
  checksum: 0feb40b602d3b525359f57ad76d64542e192a6f8bb714477b9044b5ba13ab7007a3b5a8a2b2df50e3fdcc9da0c04d07daa3797bc5cc6aad5d29d5633c5616251
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-util@npm:9.0.2":
  version: 9.0.2
  resolution: "@nomicfoundation/ethereumjs-util@npm:9.0.2"
  dependencies:
    "@chainsafe/ssz": "npm:^0.10.0"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    ethereum-cryptography: "npm:0.1.3"
  checksum: ec687ecd964bf624e82b5f0cbbffd0896dfdcff6e7b3eb216d18b79cdb6735f2c8a516d04c5a2b0e36a906bd8f35174a4653634151f35e615a7d24d901a35905
  languageName: node
  linkType: hard

"@nomicfoundation/ethereumjs-vm@npm:7.0.2":
  version: 7.0.2
  resolution: "@nomicfoundation/ethereumjs-vm@npm:7.0.2"
  dependencies:
    "@nomicfoundation/ethereumjs-block": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-blockchain": "npm:7.0.2"
    "@nomicfoundation/ethereumjs-common": "npm:4.0.2"
    "@nomicfoundation/ethereumjs-evm": "npm:2.0.2"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-statemanager": "npm:2.0.2"
    "@nomicfoundation/ethereumjs-trie": "npm:6.0.2"
    "@nomicfoundation/ethereumjs-tx": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    debug: "npm:^4.3.3"
    ethereum-cryptography: "npm:0.1.3"
    mcl-wasm: "npm:^0.7.1"
    rustbn.js: "npm:~0.2.0"
  checksum: 7ad391bc68e8e755ed0b88012e8857390590b5ba8b11bdb4995b04ba7afed5570a4259b85fff329affb643e87be12972ca44d269e14e5f840a82c315011d0d7a
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-darwin-arm64@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-darwin-arm64@npm:0.1.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-darwin-x64@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-darwin-x64@npm:0.1.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-freebsd-x64@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-freebsd-x64@npm:0.1.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-arm64-gnu@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-linux-arm64-gnu@npm:0.1.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-arm64-musl@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-linux-arm64-musl@npm:0.1.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-x64-gnu@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-linux-x64-gnu@npm:0.1.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-linux-x64-musl@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-linux-x64-musl@npm:0.1.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-win32-arm64-msvc@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-win32-arm64-msvc@npm:0.1.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-win32-ia32-msvc@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-win32-ia32-msvc@npm:0.1.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer-win32-x64-msvc@npm:0.1.1":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer-win32-x64-msvc@npm:0.1.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nomicfoundation/solidity-analyzer@npm:^0.1.0":
  version: 0.1.1
  resolution: "@nomicfoundation/solidity-analyzer@npm:0.1.1"
  dependencies:
    "@nomicfoundation/solidity-analyzer-darwin-arm64": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-darwin-x64": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-freebsd-x64": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-linux-arm64-gnu": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-linux-arm64-musl": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-linux-x64-gnu": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-linux-x64-musl": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-win32-arm64-msvc": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-win32-ia32-msvc": "npm:0.1.1"
    "@nomicfoundation/solidity-analyzer-win32-x64-msvc": "npm:0.1.1"
  dependenciesMeta:
    "@nomicfoundation/solidity-analyzer-darwin-arm64":
      optional: true
    "@nomicfoundation/solidity-analyzer-darwin-x64":
      optional: true
    "@nomicfoundation/solidity-analyzer-freebsd-x64":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-arm64-gnu":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-arm64-musl":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-x64-gnu":
      optional: true
    "@nomicfoundation/solidity-analyzer-linux-x64-musl":
      optional: true
    "@nomicfoundation/solidity-analyzer-win32-arm64-msvc":
      optional: true
    "@nomicfoundation/solidity-analyzer-win32-ia32-msvc":
      optional: true
    "@nomicfoundation/solidity-analyzer-win32-x64-msvc":
      optional: true
  checksum: a3b3b557f911791b26a352d4e11abf5cbac427de33fda7d70fda043f1994a7f63f542c1752e6415a82c5452625470ba4fbf040e7d87dddd1aa3366e79b3dc2c6
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^2.0.0":
  version: 2.2.0
  resolution: "@npmcli/agent@npm:2.2.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.1"
  checksum: 822ea077553cd9cfc5cbd6d92380b0950fcb054a7027cd1b63a33bd0cbb16b0c6626ea75d95ec0e804643c8904472d3361d2da8c2444b1fb02a9b525d9c07c41
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.0
  resolution: "@npmcli/fs@npm:3.1.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: f3a7ab3a31de65e42aeb6ed03ed035ef123d2de7af4deb9d4a003d27acc8618b57d9fb9d259fe6c28ca538032a028f37337264388ba27d26d37fff7dde22476e
  languageName: node
  linkType: hard

"@openzeppelin/contracts-upgradeable-4.7.3@npm:@openzeppelin/contracts-upgradeable@v4.7.3":
  version: 4.7.3
  resolution: "@openzeppelin/contracts-upgradeable@npm:4.7.3"
  checksum: 7c72ffeca867478b5aa8e8c7adb3d1ce114cfdc797ed4f3cd074788cf4da25d620ffffd624ac7e9d1223eecffeea9f7b79200ff70dc464cc828c470ccd12ddf1
  languageName: node
  linkType: hard

"@openzeppelin/contracts-upgradeable@npm:^4.8.1":
  version: 4.9.5
  resolution: "@openzeppelin/contracts-upgradeable@npm:4.9.5"
  checksum: 9a0d137bff231b60ca30840aea225f9baf1d8bf5f229d21d64c67af3793fd381d9dae9b66536ea8c156e46354d8d5b1d4141d33434892ba989f056c972f9e731
  languageName: node
  linkType: hard

"@openzeppelin/contracts-v0.7@npm:@openzeppelin/contracts@v3.4.2":
  version: 3.4.2
  resolution: "@openzeppelin/contracts@npm:3.4.2"
  checksum: e803e322bd111565e2de742c62f1a598c7c7dd7385c1c0fe3c06e2b4913e599024ad1d32e2693f199f5230af4d9b0eeefb389f32740006312895b962a4d937d4
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:^4.8.1":
  version: 4.9.5
  resolution: "@openzeppelin/contracts@npm:4.9.5"
  checksum: f221d91a7dd96f9187aa832f8a160d673feb2904711bd210fab56ccfd8b8351b8150b4f0bd247701f7d4adddceba83943c049c6da11d126e07164b9abff767e0
  languageName: node
  linkType: hard

"@openzeppelin/contracts@npm:~4.3.3":
  version: 4.3.3
  resolution: "@openzeppelin/contracts@npm:4.3.3"
  checksum: 14fb7f3807054033671562165086ac4d1a500cf2068a6eadad16cbeb443df508a659cc658d91bf3ea0aea64920048cc26a7ad7d2a4f7844671e3084a4fc5cf0b
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@pkgr/utils@npm:^2.4.2":
  version: 2.4.2
  resolution: "@pkgr/utils@npm:2.4.2"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    fast-glob: "npm:^3.3.0"
    is-glob: "npm:^4.0.3"
    open: "npm:^9.1.0"
    picocolors: "npm:^1.0.0"
    tslib: "npm:^2.6.0"
  checksum: f0b0b305a83bd65fac5637d28ad3e33f19194043e03ceef6b4e13d260bfa2678b73df76dc56ed906469ffe0494d4bd214e6b92ca80684f38547982edf982dd15
  languageName: node
  linkType: hard

"@pnpm/config.env-replace@npm:^1.1.0":
  version: 1.1.0
  resolution: "@pnpm/config.env-replace@npm:1.1.0"
  checksum: fabe35cede1b72ad12877b8bed32f7c2fcd89e94408792c4d69009b886671db7988a2132bc18b7157489d2d0fd4266a06c9583be3d2e10c847bf06687420cb2a
  languageName: node
  linkType: hard

"@pnpm/network.ca-file@npm:^1.0.1":
  version: 1.0.2
  resolution: "@pnpm/network.ca-file@npm:1.0.2"
  dependencies:
    graceful-fs: "npm:4.2.10"
  checksum: d8d0884646500576bd5390464d13db1bb9a62e32a1069293e5bddb2ad8354b354b7e2d2a35e12850025651e795e6a80ce9e601c66312504667b7e3ee7b52becc
  languageName: node
  linkType: hard

"@pnpm/npm-conf@npm:^2.1.0":
  version: 2.2.2
  resolution: "@pnpm/npm-conf@npm:2.2.2"
  dependencies:
    "@pnpm/config.env-replace": "npm:^1.1.0"
    "@pnpm/network.ca-file": "npm:^1.0.1"
    config-chain: "npm:^1.1.11"
  checksum: 45422fecc7ed49e5254eef744576625e27cdebccce930f42c66cf2fb70443fc24f506c3fcf4859e6371677ceb144feb45e925ec14774b54588b89806b32dea9a
  languageName: node
  linkType: hard

"@scure/base@npm:~1.1.0":
  version: 1.1.5
  resolution: "@scure/base@npm:1.1.5"
  checksum: 543fa9991c6378b6a0d5ab7f1e27b30bb9c1e860d3ac81119b4213cfdf0ad7b61be004e06506e89de7ce0cec9391c17f5c082bb34c3b617a2ee6a04129f52481
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.1.0":
  version: 1.1.0
  resolution: "@scure/bip32@npm:1.1.0"
  dependencies:
    "@noble/hashes": "npm:~1.1.1"
    "@noble/secp256k1": "npm:~1.6.0"
    "@scure/base": "npm:~1.1.0"
  checksum: e58660fc96dc5c87d0047bf41150fa3b424617e6289ba522cc81bdeecaf1a26e34f01dcd9d76f3e5c2c570ced608a527733cc375abfce4dc9b8e2365719ea5d3
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.1.5":
  version: 1.1.5
  resolution: "@scure/bip32@npm:1.1.5"
  dependencies:
    "@noble/hashes": "npm:~1.2.0"
    "@noble/secp256k1": "npm:~1.7.0"
    "@scure/base": "npm:~1.1.0"
  checksum: 4c83e943a66e7b212d18f47b4650ed9b1dfeb69d8bdd8b491b12ba70ca8635cda67fb1ac920d642d66c8a3c2c03303b623c1faceafe7141a6f20a7cd7f66191e
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.1.0":
  version: 1.1.0
  resolution: "@scure/bip39@npm:1.1.0"
  dependencies:
    "@noble/hashes": "npm:~1.1.1"
    "@scure/base": "npm:~1.1.0"
  checksum: d843be225dda4b6b2c0f90e52e00eef708df3cecbc944902298d487c669a6d219bd41877b20adaf72ba84aec2f0cb1e4567dafc6ce7295d9f132bdb0dcb375b3
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.1.1":
  version: 1.1.1
  resolution: "@scure/bip39@npm:1.1.1"
  dependencies:
    "@noble/hashes": "npm:~1.2.0"
    "@scure/base": "npm:~1.1.0"
  checksum: 08908145e0890e481e3398191424961d9ebfb8913fed6e6cdfc63eb1281bd1895244d46c0e8762b0e30d8dc6f498ed296311382fecbf034253838e3a50f60ca1
  languageName: node
  linkType: hard

"@sentry/core@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/core@npm:5.30.0"
  dependencies:
    "@sentry/hub": "npm:5.30.0"
    "@sentry/minimal": "npm:5.30.0"
    "@sentry/types": "npm:5.30.0"
    "@sentry/utils": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: fef7808017cc9581e94c51fbce3ffeb6bdb62b30d94920fae143d298aed194176ac7c026923d569a33606b93a3747b877e78215a1668ed8eb44e5941527e17e0
  languageName: node
  linkType: hard

"@sentry/hub@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/hub@npm:5.30.0"
  dependencies:
    "@sentry/types": "npm:5.30.0"
    "@sentry/utils": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: b0e21a7acb1c363a3097c7578dd483b2e534bc62541977da7d3c643703767bbcfd65831b70b102fefa715e6b75004ca1dab680d117e1a7455e839042118c1051
  languageName: node
  linkType: hard

"@sentry/minimal@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/minimal@npm:5.30.0"
  dependencies:
    "@sentry/hub": "npm:5.30.0"
    "@sentry/types": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: e74bf519f5e284decb81eea8fd7c75b02827bde36c8ccef5ad0b941043e62a6d6578d7f1ad9dba33e03d240593140990b1999215a35abb344e2b4f3e09b15c90
  languageName: node
  linkType: hard

"@sentry/node@npm:^5.18.1":
  version: 5.30.0
  resolution: "@sentry/node@npm:5.30.0"
  dependencies:
    "@sentry/core": "npm:5.30.0"
    "@sentry/hub": "npm:5.30.0"
    "@sentry/tracing": "npm:5.30.0"
    "@sentry/types": "npm:5.30.0"
    "@sentry/utils": "npm:5.30.0"
    cookie: "npm:^0.4.1"
    https-proxy-agent: "npm:^5.0.0"
    lru_map: "npm:^0.3.3"
    tslib: "npm:^1.9.3"
  checksum: 9fa37b3ce646954f68e4b7506d17c67f5779c69cd432801aaf6796f9ecea9632eb8729b77b71a31dcd5a9f57fb7759fd213222955a667d8ad557df6e997a00c4
  languageName: node
  linkType: hard

"@sentry/tracing@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/tracing@npm:5.30.0"
  dependencies:
    "@sentry/hub": "npm:5.30.0"
    "@sentry/minimal": "npm:5.30.0"
    "@sentry/types": "npm:5.30.0"
    "@sentry/utils": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: 7e74a29823b445adb104c323324348882987554d049e83e5d3439149d2677024350974161c28b1a55a2750509b030525f81056a48427be06183f3744220ba4b0
  languageName: node
  linkType: hard

"@sentry/types@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/types@npm:5.30.0"
  checksum: 3ca60689871b298dbab16c1bb6fb4637f72d3c21820017bac9df1765fd560004862cc9e75fb438e5714048b3a9bc641c396cdbb3c3573ac62481d2ea83f1da6d
  languageName: node
  linkType: hard

"@sentry/utils@npm:5.30.0":
  version: 5.30.0
  resolution: "@sentry/utils@npm:5.30.0"
  dependencies:
    "@sentry/types": "npm:5.30.0"
    tslib: "npm:^1.9.3"
  checksum: 4aa8acf7d0d9688c927a620cbb9fd37d6d2738f701863af772be329baca2cede909dcae6c7b4b449474787245c09212909ee740b4cae143d21ddb1fed910cc3a
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^4.0.0, @sindresorhus/is@npm:^4.6.0":
  version: 4.6.0
  resolution: "@sindresorhus/is@npm:4.6.0"
  checksum: e7f36ed72abfcd5e0355f7423a72918b9748bb1ef370a59f3e5ad8d40b728b85d63b272f65f63eec1faf417cda89dcb0aeebe94015647b6054659c1442fe5ce0
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^5.2.0":
  version: 5.6.0
  resolution: "@sindresorhus/is@npm:5.6.0"
  checksum: b077c325acec98e30f7d86df158aaba2e7af2acb9bb6a00fda4b91578539fbff4ecebe9b934e24fec0e6950de3089d89d79ec02d9062476b20ce185be0e01bd6
  languageName: node
  linkType: hard

"@solidity-parser/parser@npm:^0.16.0, @solidity-parser/parser@npm:^0.16.2":
  version: 0.16.2
  resolution: "@solidity-parser/parser@npm:0.16.2"
  dependencies:
    antlr4ts: "npm:^0.5.0-alpha.4"
  checksum: a95b0c45331623e587e938e69d49832814ab8867371bd58f9946346b99a18705ee47b98a1006c3da6dae33ec8c8c1bf7d203a4acfa1ced0bfd68632acf2cd19c
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^4.0.5":
  version: 4.0.6
  resolution: "@szmarczak/http-timer@npm:4.0.6"
  dependencies:
    defer-to-connect: "npm:^2.0.0"
  checksum: c29df3bcec6fc3bdec2b17981d89d9c9fc9bd7d0c9bcfe92821dc533f4440bc890ccde79971838b4ceed1921d456973c4180d7175ee1d0023ad0562240a58d95
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^5.0.1":
  version: 5.0.1
  resolution: "@szmarczak/http-timer@npm:5.0.1"
  dependencies:
    defer-to-connect: "npm:^2.0.1"
  checksum: fc9cb993e808806692e4a3337c90ece0ec00c89f4b67e3652a356b89730da98bc824273a6d67ca84d5f33cd85f317dcd5ce39d8cc0a2f060145a608a7cb8ce92
  languageName: node
  linkType: hard

"@truffle/hdwallet-provider@npm:latest":
  version: 2.1.15
  resolution: "@truffle/hdwallet-provider@npm:2.1.15"
  dependencies:
    "@ethereumjs/common": "npm:^2.4.0"
    "@ethereumjs/tx": "npm:^3.3.0"
    "@metamask/eth-sig-util": "npm:4.0.1"
    "@truffle/hdwallet": "npm:^0.1.4"
    "@types/ethereum-protocol": "npm:^1.0.0"
    "@types/web3": "npm:1.0.20"
    "@types/web3-provider-engine": "npm:^14.0.0"
    ethereum-cryptography: "npm:1.1.2"
    ethereum-protocol: "npm:^1.0.1"
    ethereumjs-util: "npm:^7.1.5"
    web3: "npm:1.10.0"
    web3-provider-engine: "npm:16.0.3"
  checksum: 716346070f811449412575d5c8075315decd2c16a11aded213dfd8d64cf74ae9abba0204f6bf4886cab8dd593e5be20b6e4224c3d0730fb8f589e906e2300567
  languageName: node
  linkType: hard

"@truffle/hdwallet@npm:^0.1.4":
  version: 0.1.4
  resolution: "@truffle/hdwallet@npm:0.1.4"
  dependencies:
    ethereum-cryptography: "npm:1.1.2"
    keccak: "npm:3.0.2"
    secp256k1: "npm:4.0.3"
  checksum: 04ffa4d362d5c3ce3a09c04ecca3d570646e7044e550e368b1ebf3a0716f1339f71bf9e5f0cd6ae0a026a94fbda35bdea3aed8762788122e0df477ae5af31c02
  languageName: node
  linkType: hard

"@types/bn.js@npm:*, @types/bn.js@npm:^5.1.0, @types/bn.js@npm:^5.1.1":
  version: 5.1.5
  resolution: "@types/bn.js@npm:5.1.5"
  dependencies:
    "@types/node": "npm:*"
  checksum: 9719330c86aeae0a6a447c974cf0f853ba3660ede20de61f435b03d699e30e6d8b35bf71a8dc9fdc8317784438e83177644ba068ed653d0ae0106e1ecbfe289e
  languageName: node
  linkType: hard

"@types/bn.js@npm:^4.11.3":
  version: 4.11.6
  resolution: "@types/bn.js@npm:4.11.6"
  dependencies:
    "@types/node": "npm:*"
  checksum: 9ff3e7a1539a953c381c0d30ea2049162e3cab894cda91ee10f3a84d603f9afa2b2bc2a38fe9b427de94b6e2b7b77aefd217c1c7b07a10ae8d7499f9d6697a41
  languageName: node
  linkType: hard

"@types/cacheable-request@npm:^6.0.1, @types/cacheable-request@npm:^6.0.2":
  version: 6.0.3
  resolution: "@types/cacheable-request@npm:6.0.3"
  dependencies:
    "@types/http-cache-semantics": "npm:*"
    "@types/keyv": "npm:^3.1.4"
    "@types/node": "npm:*"
    "@types/responselike": "npm:^1.0.0"
  checksum: 159f9fdb2a1b7175eef453ae2ced5ea04c0d2b9610cc9ccd9f9abb066d36dacb1f37acd879ace10ad7cbb649490723feb396fb7307004c9670be29636304b988
  languageName: node
  linkType: hard

"@types/ethereum-protocol@npm:*, @types/ethereum-protocol@npm:^1.0.0":
  version: 1.0.5
  resolution: "@types/ethereum-protocol@npm:1.0.5"
  dependencies:
    bignumber.js: "npm:7.2.1"
  checksum: 52a75fd91042b2408db449a4a1d26622b6e81d371aa37dbb329638d5e3d78ed0c54cb0e4853fb257c95daa12cbc8be0afa2b41277991bf2cae95153b335d0599
  languageName: node
  linkType: hard

"@types/http-cache-semantics@npm:*, @types/http-cache-semantics@npm:^4.0.2":
  version: 4.0.4
  resolution: "@types/http-cache-semantics@npm:4.0.4"
  checksum: a59566cff646025a5de396d6b3f44a39ab6a74f2ed8150692e0f31cc52f3661a68b04afe3166ebe0d566bd3259cb18522f46e949576d5204781cd6452b7fe0c5
  languageName: node
  linkType: hard

"@types/keyv@npm:^3.1.4":
  version: 3.1.4
  resolution: "@types/keyv@npm:3.1.4"
  dependencies:
    "@types/node": "npm:*"
  checksum: e009a2bfb50e90ca9b7c6e8f648f8464067271fd99116f881073fa6fa76dc8d0133181dd65e6614d5fb1220d671d67b0124aef7d97dc02d7e342ab143a47779d
  languageName: node
  linkType: hard

"@types/lru-cache@npm:^5.1.0":
  version: 5.1.1
  resolution: "@types/lru-cache@npm:5.1.1"
  checksum: 0afadefc983306684a8ef95b6337a0d9e3f687e7e89e1f1f3f2e1ce3fbab5b018bb84cf277d781f871175a2c8f0176762b69e58b6f4296ee1b816cea94d5ef06
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 20.10.4
  resolution: "@types/node@npm:20.10.4"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: c10c1dd13f5c2341ad866777dc32946538a99e1ebd203ae127730814b8e5fa4aedfbcb01cb3e24a5466f1af64bcdfa16e7de6e745ff098fff0942aa779b7fe03
  languageName: node
  linkType: hard

"@types/node@npm:^12.12.6":
  version: 12.20.55
  resolution: "@types/node@npm:12.20.55"
  checksum: 1f916a06fff02faadb09a16ed6e31820ce170798b202ef0b14fc244bfbd721938c54a3a99836e185e4414ca461fe96c5bb5c67c3d248f153555b7e6347f061dd
  languageName: node
  linkType: hard

"@types/pbkdf2@npm:^3.0.0":
  version: 3.1.2
  resolution: "@types/pbkdf2@npm:3.1.2"
  dependencies:
    "@types/node": "npm:*"
  checksum: bebe1e596cbbe5f7d2726a58859e61986c5a42459048e29cb7f2d4d764be6bbb0844572fd5d70ca8955a8a17e8b4ed80984fc4903e165d9efb8807a3fbb051aa
  languageName: node
  linkType: hard

"@types/qs@npm:^6.9.7":
  version: 6.9.10
  resolution: "@types/qs@npm:6.9.10"
  checksum: 3e479ee056bd2b60894baa119d12ecd33f20a25231b836af04654e784c886f28a356477630430152a86fba253da65d7ecd18acffbc2a8877a336e75aa0272c67
  languageName: node
  linkType: hard

"@types/readable-stream@npm:^2.3.13":
  version: 2.3.15
  resolution: "@types/readable-stream@npm:2.3.15"
  dependencies:
    "@types/node": "npm:*"
    safe-buffer: "npm:~5.1.1"
  checksum: 49b51e56f9cc401cb31c72973a7565ef4208d7e2465a789843104ec0fcbe609727b0b5bf4682fbec773c7f7bd14858e5dba739fd85e14d8a85e41185d65984d3
  languageName: node
  linkType: hard

"@types/responselike@npm:^1.0.0":
  version: 1.0.3
  resolution: "@types/responselike@npm:1.0.3"
  dependencies:
    "@types/node": "npm:*"
  checksum: 6ac4b35723429b11b117e813c7acc42c3af8b5554caaf1fc750404c1ae59f9b7376bc69b9e9e194a5a97357a597c2228b7173d317320f0360d617b6425212f58
  languageName: node
  linkType: hard

"@types/secp256k1@npm:^4.0.1":
  version: 4.0.6
  resolution: "@types/secp256k1@npm:4.0.6"
  dependencies:
    "@types/node": "npm:*"
  checksum: 211f823be990b55612e604d620acf0dc3bc942d3836bdd8da604269effabc86d98161e5947487b4e4e128f9180fc1682daae2f89ea7a4d9648fdfe52fba365fc
  languageName: node
  linkType: hard

"@types/underscore@npm:*":
  version: 1.11.15
  resolution: "@types/underscore@npm:1.11.15"
  checksum: 5a9c262566551f61744e066d33578c0fad1139c2d5314ed77a8f867487a6d7dd18616f32df4c605070fbd569b942c6d2c083be9f9ae649f41a562b52812b06be
  languageName: node
  linkType: hard

"@types/web3-provider-engine@npm:^14.0.0":
  version: 14.0.4
  resolution: "@types/web3-provider-engine@npm:14.0.4"
  dependencies:
    "@types/ethereum-protocol": "npm:*"
  checksum: c267a3b962708740c4ebeb3801f4115f7c278a6d526719fbfb336caed96141997f185719bfd9835a4ffc15cbd316b0b6562756a6db95ae6f4b5cd3e3a51da85a
  languageName: node
  linkType: hard

"@types/web3@npm:1.0.20":
  version: 1.0.20
  resolution: "@types/web3@npm:1.0.20"
  dependencies:
    "@types/bn.js": "npm:*"
    "@types/underscore": "npm:*"
  checksum: c9db2e3d13d4d6d0be6cb00ec2306be908a31227c433a0ab2df1d1afb66ffc948956d742a13814e5eb2b6e6994ffb20d4dfb6f2b29e1eb01e9a0b474149c4216
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: ca0a54e35bea4ece0ecb68a47b312e1a9a6f772408d5bcb9051230aaa94b0460671c5b5c9cb3240eb5b7bc94c52476550eb221f65a0bbd0145bdc9f3113a6707
  languageName: node
  linkType: hard

"abortcontroller-polyfill@npm:^1.7.3":
  version: 1.7.5
  resolution: "abortcontroller-polyfill@npm:1.7.5"
  checksum: aac398f7fc076235fe731adaffd2c319fe6c1527af8ca561890242d5396351350e0705726478778dc90326a69a4c044890c156fe867cba7f3ffeb670f8665a51
  languageName: node
  linkType: hard

"abstract-level@npm:^1.0.0, abstract-level@npm:^1.0.2, abstract-level@npm:^1.0.3":
  version: 1.0.3
  resolution: "abstract-level@npm:1.0.3"
  dependencies:
    buffer: "npm:^6.0.3"
    catering: "npm:^2.1.0"
    is-buffer: "npm:^2.0.5"
    level-supports: "npm:^4.0.0"
    level-transcoder: "npm:^1.0.1"
    module-error: "npm:^1.0.1"
    queue-microtask: "npm:^1.2.3"
  checksum: a6872010a7be78240e1e5bf24b202950adbbd2a382970e17cc661ac8a73663327c241dc25f2863e599f3f5b24d0c3c357b5af4092c4ce34511bae1c09283a278
  languageName: node
  linkType: hard

"abstract-leveldown@npm:~2.6.0":
  version: 2.6.3
  resolution: "abstract-leveldown@npm:2.6.3"
  dependencies:
    xtend: "npm:~4.0.0"
  checksum: 3f9db6c3c77991018adcad133acb2e084446d7583753c46343ec3ba6ff60ab89cd1aa3813b2131ccc92191863802cacbca3240490fadde20118845267643c543
  languageName: node
  linkType: hard

"abstract-leveldown@npm:~2.7.1":
  version: 2.7.2
  resolution: "abstract-leveldown@npm:2.7.2"
  dependencies:
    xtend: "npm:~4.0.0"
  checksum: cac79f5e24b91259706501d5e9881b63418623d3d192a42c7aa8ab427c5403142f81c6ce3649169bdf16514aea06b5ae7e2d6f4d22740ad967b48f2fa319e3f0
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 67eaaa90e2917c58418e7a9b89392002d2b1ccd69bcca4799135d0c632f3b082f23f4ae4ddeedbced5aa59bcc7bdf4699c69ebed4593696c922462b7bc5744d6
  languageName: node
  linkType: hard

"adm-zip@npm:^0.4.16":
  version: 0.4.16
  resolution: "adm-zip@npm:0.4.16"
  checksum: 897003d21a445bfce251d5a328706035dc03af53cd4c66bb0a4558496939f89767ae5e7c67d10a5a9ad0146081a339bed3361405d6cca648a4378198573e9cad
  languageName: node
  linkType: hard

"aes-js@npm:3.0.0":
  version: 3.0.0
  resolution: "aes-js@npm:3.0.0"
  checksum: 1b3772e5ba74abdccb6c6b99bf7f50b49057b38c0db1612b46c7024414f16e65ba7f1643b2d6e38490b1870bdf3ba1b87b35e2c831fd3fdaeff015f08aad19d1
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 21fb903e0917e5cb16591b4d0ef6a028a54b83ac30cd1fca58dece3d4e0990512a8723f9f83130d88a41e2af8b1f7be1386fda3ea2d181bb1a62155e75e95e23
  languageName: node
  linkType: hard

"agent-base@npm:^7.0.2, agent-base@npm:^7.1.0":
  version: 7.1.0
  resolution: "agent-base@npm:7.1.0"
  dependencies:
    debug: "npm:^4.3.4"
  checksum: f7828f991470a0cc22cb579c86a18cbae83d8a3cbed39992ab34fc7217c4d126017f1c74d0ab66be87f71455318a8ea3e757d6a37881b8d0f2a2c6aa55e5418f
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv@npm:^6.12.3, ajv@npm:^6.12.6":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"ajv@npm:^8.0.1":
  version: 8.12.0
  resolution: "ajv@npm:8.12.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
    uri-js: "npm:^4.2.2"
  checksum: b406f3b79b5756ac53bfe2c20852471b08e122bc1ee4cde08ae4d6a800574d9cd78d60c81c69c63ff81e4da7cd0b638fafbb2303ae580d49cf1600b9059efb85
  languageName: node
  linkType: hard

"ansi-colors@npm:4.1.1":
  version: 4.1.1
  resolution: "ansi-colors@npm:4.1.1"
  checksum: e862fddd0a9ca88f1e7c9312ea70674cec3af360c994762309f6323730525e92c77d2715ee5f08aa8f438b7ca18efe378af647f501fc92b15b8e4b3b52d09db4
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.1":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: 43d6e2fc7b1c6e4dc373de708ee76311ec2e0433e7e8bd3194e7ff123ea6a747428fc61afdcf5969da5be3a5f0fd054602bec56fc0ebe249ce2fcde6e649e3c2
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.0":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 8661034456193ffeda0c15c8c564a9636b0c04094b7f78bd01517929c17c504090a60f7a75f949f5af91289c264d3e1001d91492c1bd58efc8e100500ce04de2
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"antlr4@npm:^4.11.0":
  version: 4.13.1
  resolution: "antlr4@npm:4.13.1"
  checksum: b17ee045cf30c7cec563f3ce81dc8bffd2c17b546646cd76e7b5f4cc4b676a1960c1a8a9aea53769947b0c6de70ed2aea73431e76c3840ab3b69cf3de5f1fc89
  languageName: node
  linkType: hard

"antlr4ts@npm:^0.5.0-alpha.4":
  version: 0.5.0-dev
  resolution: "antlr4ts@npm:0.5.0-dev"
  dependencies:
    source-map-support: "npm:^0.5.16"
  checksum: a95a061fb2fc9e2a0cd065e112fbc3fb899f408feace51249367051711b2255488b4e89b5912a706080f807c72484499e0f61f6a782391ecaba39c556d479f55
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"asn1@npm:~0.2.3":
  version: 0.2.6
  resolution: "asn1@npm:0.2.6"
  dependencies:
    safer-buffer: "npm:~2.1.0"
  checksum: cf629291fee6c1a6f530549939433ebf32200d7849f38b810ff26ee74235e845c0c12b2ed0f1607ac17383d19b219b69cefa009b920dab57924c5c544e495078
  languageName: node
  linkType: hard

"assert-plus@npm:1.0.0, assert-plus@npm:^1.0.0":
  version: 1.0.0
  resolution: "assert-plus@npm:1.0.0"
  checksum: f4f991ae2df849cc678b1afba52d512a7cbf0d09613ba111e72255409ff9158550c775162a47b12d015d1b82b3c273e8e25df0e4783d3ddb008a293486d00a07
  languageName: node
  linkType: hard

"assertion-error@npm:^1.1.0":
  version: 1.1.0
  resolution: "assertion-error@npm:1.1.0"
  checksum: fd9429d3a3d4fd61782eb3962ae76b6d08aa7383123fca0596020013b3ebd6647891a85b05ce821c47d1471ed1271f00b0545cf6a4326cf2fc91efcc3b0fbecf
  languageName: node
  linkType: hard

"ast-parents@npm:^0.0.1":
  version: 0.0.1
  resolution: "ast-parents@npm:0.0.1"
  checksum: 08eaa3b755529aad0708aad54ff09087b171334dcffa0774d3401e1dc54db1242bd5e76e599152705e813f768b9245a3c20777ed033c706d2093e358a91b12c2
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"async-eventemitter@npm:^0.2.2":
  version: 0.2.4
  resolution: "async-eventemitter@npm:0.2.4"
  dependencies:
    async: "npm:^2.4.0"
  checksum: 4f927de88add821cb11640dcbbc8bad561dace016b661ad8d597b60641d57cee740477a34ba9832b60f89a93cad43e78a3eb881f00fe0da49a85844a7b9de026
  languageName: node
  linkType: hard

"async-limiter@npm:~1.0.0":
  version: 1.0.1
  resolution: "async-limiter@npm:1.0.1"
  checksum: 2b849695b465d93ad44c116220dee29a5aeb63adac16c1088983c339b0de57d76e82533e8e364a93a9f997f28bbfc6a92948cefc120652bd07f3b59f8d75cf2b
  languageName: node
  linkType: hard

"async-mutex@npm:^0.2.6":
  version: 0.2.6
  resolution: "async-mutex@npm:0.2.6"
  dependencies:
    tslib: "npm:^2.0.0"
  checksum: 3cf676fc48b4686abf534cc02d4784bab3f35d7836a0a7476c96e57c3f6607dd3d94cc0989b29d33ce5ae5cde8be8e1a96f3e769ba3b0e1ba4a244f873aa5623
  languageName: node
  linkType: hard

"async@npm:^1.4.2":
  version: 1.5.2
  resolution: "async@npm:1.5.2"
  checksum: 8afcdcee05168250926a3e7bd4dfaa74b681a74f634bae2af424fb716042461cbd20a375d9bc2534daa50a2d45286c9b174952fb239cee4ab8d6351a40c65327
  languageName: node
  linkType: hard

"async@npm:^2.0.1, async@npm:^2.1.2, async@npm:^2.4.0, async@npm:^2.5.0":
  version: 2.6.4
  resolution: "async@npm:2.6.4"
  dependencies:
    lodash: "npm:^4.17.14"
  checksum: df8e52817d74677ab50c438d618633b9450aff26deb274da6dfedb8014130909482acdc7753bce9b72e6171ce9a9f6a92566c4ced34c3cb3714d57421d58ad27
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 3ce727cbc78f69d6a4722517a58ee926c8c21083633b1d3fdf66fd688f6c127a53a592141bd4866f9b63240a86e9d8e974b13919450bd17fa33c2d22c4558ad8
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.5":
  version: 1.0.5
  resolution: "available-typed-arrays@npm:1.0.5"
  checksum: 4d4d5e86ea0425696f40717882f66a570647b94ac8d273ddc7549a9b61e5da099e149bf431530ccbd776bd74e02039eb8b5edf426e3e2211ee61af16698a9064
  languageName: node
  linkType: hard

"aws-sign2@npm:~0.7.0":
  version: 0.7.0
  resolution: "aws-sign2@npm:0.7.0"
  checksum: 2ac497d739f71be3264cf096a33ab256a1fea7fe80b87dc51ec29374505bd5a661279ef1c22989d68528ea61ed634021ca63b31cf1d3c2a3682ffc106f7d0e96
  languageName: node
  linkType: hard

"aws4@npm:^1.8.0":
  version: 1.12.0
  resolution: "aws4@npm:1.12.0"
  checksum: 2b8455fe1eee87f0e7d5f32e81e7fec74dce060c72d03f528c8c631fa74209cef53aab6fede182ea17d0c9520cb1e5e3023c5fedb4f1139ae9f067fc720869a5
  languageName: node
  linkType: hard

"axios@npm:^0.21.1":
  version: 0.21.4
  resolution: "axios@npm:0.21.4"
  dependencies:
    follow-redirects: "npm:^1.14.0"
  checksum: da644592cb6f8f9f8c64fdabd7e1396d6769d7a4c1ea5f8ae8beb5c2eb90a823e3a574352b0b934ac62edc762c0f52647753dc54f7d07279127a7e5c4cd20272
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.6":
  version: 0.4.7
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.7"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.4.4"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 3b61cdb275592f61b29d582ee8c738a13d9897c5dd201cddb0610b381f3ae139ebc988ac96f72978fc143c3d50c15d46618df865822e282c8e76c236e7378b63
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.8.5":
  version: 0.8.7
  resolution: "babel-plugin-polyfill-corejs3@npm:0.8.7"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.4.4"
    core-js-compat: "npm:^3.33.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: defbc6de3d309c9639dd31223b5011707fcc0384037ac5959a1aefe16eb314562e1c1e5cfbce0af14a220d639ef92dfe5baf66664e9e6054656aca2841677622
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.5.3":
  version: 0.5.4
  resolution: "babel-plugin-polyfill-regenerator@npm:0.5.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.4.4"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 461b735c6c0eca3c7b4434d14bfa98c2ab80f00e2bdc1c69eb46d1d300092a9786d76bbd3ee55e26d2d1a2380c14592d8d638e271dfd2a2b78a9eacffa3645d1
  languageName: node
  linkType: hard

"backoff@npm:^2.5.0":
  version: 2.5.0
  resolution: "backoff@npm:2.5.0"
  dependencies:
    precond: "npm:0.2"
  checksum: 5286c3f02665f3347591e04728ba0755e76a45aa40e037f7db3f2029ede927bfe94755a03033e93cc971a4b6c6605d8cfe514433e362c5a86c0b5bbc5d47acce
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base-x@npm:^3.0.2, base-x@npm:^3.0.8":
  version: 3.0.9
  resolution: "base-x@npm:3.0.9"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 957101d6fd09e1903e846fd8f69fd7e5e3e50254383e61ab667c725866bec54e5ece5ba49ce385128ae48f9ec93a26567d1d5ebb91f4d56ef4a9cc0d5a5481e8
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"bcrypt-pbkdf@npm:^1.0.0":
  version: 1.0.2
  resolution: "bcrypt-pbkdf@npm:1.0.2"
  dependencies:
    tweetnacl: "npm:^0.14.3"
  checksum: 13a4cde058250dbf1fa77a4f1b9a07d32ae2e3b9e28e88a0c7a1827835bc3482f3e478c4a0cfd4da6ff0c46dae07da1061123a995372b32cc563d9975f975404
  languageName: node
  linkType: hard

"bech32@npm:1.1.4":
  version: 1.1.4
  resolution: "bech32@npm:1.1.4"
  checksum: 63ff37c0ce43be914c685ce89700bba1589c319af0dac1ea04f51b33d0e5ecfd40d14c24f527350b94f0a4e236385373bb9122ec276410f354ddcdbf29ca13f4
  languageName: node
  linkType: hard

"big-integer@npm:^1.6.44":
  version: 1.6.52
  resolution: "big-integer@npm:1.6.52"
  checksum: 4bc6ae152a96edc9f95020f5fc66b13d26a9ad9a021225a9f0213f7e3dc44269f423aa8c42e19d6ac4a63bb2b22140b95d10be8f9ca7a6d9aa1b22b330d1f514
  languageName: node
  linkType: hard

"bigint-crypto-utils@npm:^3.0.23":
  version: 3.3.0
  resolution: "bigint-crypto-utils@npm:3.3.0"
  checksum: 94d10ac9db66b093c7c2beace833ac167b57188c8ac784a7e207ea4f585cf9c2066e5d1f5a1b26cb6ccb7f7be8e38687c79f049b87df07cfdc7bd484aee2390d
  languageName: node
  linkType: hard

"bignumber.js@npm:7.2.1":
  version: 7.2.1
  resolution: "bignumber.js@npm:7.2.1"
  checksum: 9bfd02d93cba7d61607b9d52ffb3176844af5712f7f6a0530350daf7852894215fbaa8e6090a9fc841b297b39d475abb90451d3533bc939d1e96a4bfe293e226
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.0.0":
  version: 9.1.2
  resolution: "bignumber.js@npm:9.1.2"
  checksum: d89b8800a987225d2c00dcbf8a69dc08e92aa0880157c851c287b307d31ceb2fc2acb0c62c3e3a3d42b6c5fcae9b004035f13eb4386e56d529d7edac18d5c9d8
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: ccd267956c58d2315f5d3ea6757cf09863c5fc703e50fbeb13a7dc849b812ef76e3cf9ca8f35a0c48498776a7478d7b4a0418e1e2b8cb9cb9731f2922aaad7f8
  languageName: node
  linkType: hard

"blakejs@npm:^1.1.0":
  version: 1.2.1
  resolution: "blakejs@npm:1.2.1"
  checksum: 0638b1bd058b21892633929c43005aa6a4cc4b2ac5b338a146c3c076622f1b360795bd7a4d1f077c9b01863ed2df0c1504a81c5b520d164179120434847e6cd7
  languageName: node
  linkType: hard

"bluebird@npm:^3.5.0":
  version: 3.7.2
  resolution: "bluebird@npm:3.7.2"
  checksum: 007c7bad22c5d799c8dd49c85b47d012a1fe3045be57447721e6afbd1d5be43237af1db62e26cb9b0d9ba812d2e4ca3bac82f6d7e016b6b88de06ee25ceb96e7
  languageName: node
  linkType: hard

"bn.js@npm:4.11.6":
  version: 4.11.6
  resolution: "bn.js@npm:4.11.6"
  checksum: 22741b015c9fff60fce32fc9988331b298eb9b6db5bfb801babb23b846eaaf894e440e0d067b2b3ae4e46aab754e90972f8f333b31bf94a686bbcb054bfa7b14
  languageName: node
  linkType: hard

"bn.js@npm:^4.11.0, bn.js@npm:^4.11.6, bn.js@npm:^4.11.8, bn.js@npm:^4.11.9":
  version: 4.12.0
  resolution: "bn.js@npm:4.12.0"
  checksum: 10f8db196d3da5adfc3207d35d0a42aa29033eb33685f20ba2c36cadfe2de63dad05df0a20ab5aae01b418d1c4b3d4d205273085262fa020d17e93ff32b67527
  languageName: node
  linkType: hard

"bn.js@npm:^5.1.2, bn.js@npm:^5.2.0, bn.js@npm:^5.2.1":
  version: 5.2.1
  resolution: "bn.js@npm:5.2.1"
  checksum: 7a7e8764d7a6e9708b8b9841b2b3d6019cc154d2fc23716d0efecfe1e16921b7533c6f7361fb05471eab47986c4aa310c270f88e3507172104632ac8df2cfd84
  languageName: node
  linkType: hard

"body-parser@npm:1.20.1":
  version: 1.20.1
  resolution: "body-parser@npm:1.20.1"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.4"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.11.0"
    raw-body: "npm:2.5.1"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 5f8d128022a2fb8b6e7990d30878a0182f300b70e46b3f9d358a9433ad6275f0de46add6d63206da3637c01c3b38b6111a7480f7e7ac2e9f7b989f6133fe5510
  languageName: node
  linkType: hard

"body-parser@npm:^1.16.0":
  version: 1.20.2
  resolution: "body-parser@npm:1.20.2"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.11.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 3cf171b82190cf91495c262b073e425fc0d9e25cc2bf4540d43f7e7bbca27d6a9eae65ca367b6ef3993eea261159d9d2ab37ce444e8979323952e12eb3df319a
  languageName: node
  linkType: hard

"bplist-parser@npm:^0.2.0":
  version: 0.2.0
  resolution: "bplist-parser@npm:0.2.0"
  dependencies:
    big-integer: "npm:^1.6.44"
  checksum: 15d31c1b0c7e0fb384e96349453879a33609d92d91b55a9ccee04b4be4b0645f1c823253d73326a1a23104521fbc45c2dd97fb05adf61863841b68cbb2ca7a3d
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: "npm:^7.0.1"
  checksum: 966b1fb48d193b9d155f810e5efd1790962f2c4e0829f8440b8ad236ba009222c501f70185ef732fef17a4c490bb33a03b90dab0631feafbdf447da91e8165b1
  languageName: node
  linkType: hard

"brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 8a05c9f3c4b46572dec6ef71012b1946db6cae8c7bb60ccd4b7dd5a84655db49fe043ecc6272e7ef1f69dc53d6730b9e2a3a03a8310509a3d797a618cbee52be
  languageName: node
  linkType: hard

"browser-level@npm:^1.0.1":
  version: 1.0.1
  resolution: "browser-level@npm:1.0.1"
  dependencies:
    abstract-level: "npm:^1.0.2"
    catering: "npm:^2.1.1"
    module-error: "npm:^1.0.2"
    run-parallel-limit: "npm:^1.1.0"
  checksum: e712569111782da76853fecf648b43ff878ff2301c2830a9e7399685b646824a85f304dea5f023e02ee41a63a972f9aad734bd411069095adc9c79784fc649a5
  languageName: node
  linkType: hard

"browser-stdout@npm:1.3.1":
  version: 1.3.1
  resolution: "browser-stdout@npm:1.3.1"
  checksum: ac70a84e346bb7afc5045ec6f22f6a681b15a4057447d4cc1c48a25c6dedb302a49a46dd4ddfb5cdd9c96e0c905a8539be1b98ae7bc440512152967009ec7015
  languageName: node
  linkType: hard

"browserify-aes@npm:^1.2.0":
  version: 1.2.0
  resolution: "browserify-aes@npm:1.2.0"
  dependencies:
    buffer-xor: "npm:^1.0.3"
    cipher-base: "npm:^1.0.0"
    create-hash: "npm:^1.1.0"
    evp_bytestokey: "npm:^1.0.3"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 2813058f74e083a00450b11ea9d5d1f072de7bf0133f5d122d4ff7b849bece56d52b9c51ad0db0fad21c0bc4e8272fd5196114bbe7b94a9b7feb0f9fbb33a3bf
  languageName: node
  linkType: hard

"browserslist@npm:^4.22.2":
  version: 4.22.2
  resolution: "browserslist@npm:4.22.2"
  dependencies:
    caniuse-lite: "npm:^1.0.30001565"
    electron-to-chromium: "npm:^1.4.601"
    node-releases: "npm:^2.0.14"
    update-browserslist-db: "npm:^1.0.13"
  bin:
    browserslist: cli.js
  checksum: e3590793db7f66ad3a50817e7b7f195ce61e029bd7187200244db664bfbe0ac832f784e4f6b9c958aef8ea4abe001ae7880b7522682df521f4bc0a5b67660b5e
  languageName: node
  linkType: hard

"bs58@npm:^4.0.0":
  version: 4.0.1
  resolution: "bs58@npm:4.0.1"
  dependencies:
    base-x: "npm:^3.0.2"
  checksum: b3c5365bb9e0c561e1a82f1a2d809a1a692059fae016be233a6127ad2f50a6b986467c3a50669ce4c18929dcccb297c5909314dd347a25a68c21b68eb3e95ac2
  languageName: node
  linkType: hard

"bs58check@npm:^2.1.2":
  version: 2.1.2
  resolution: "bs58check@npm:2.1.2"
  dependencies:
    bs58: "npm:^4.0.0"
    create-hash: "npm:^1.1.0"
    safe-buffer: "npm:^5.1.2"
  checksum: 43bdf08a5dd04581b78f040bc4169480e17008da482ffe2a6507327bbc4fc5c28de0501f7faf22901cfe57fbca79cbb202ca529003fedb4cb8dccd265b38e54d
  languageName: node
  linkType: hard

"btoa@npm:^1.2.1":
  version: 1.2.1
  resolution: "btoa@npm:1.2.1"
  bin:
    btoa: bin/btoa.js
  checksum: 29f2ca93837e10427184626bdfd5d00065dff28b604b822aa9849297dac8c8d6ad385cc96eed812ebf153d80c24a4556252afdbb97c7a712938baeaad7547705
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer-to-arraybuffer@npm:^0.0.5":
  version: 0.0.5
  resolution: "buffer-to-arraybuffer@npm:0.0.5"
  checksum: df16190b3bf0ecdf70e761514ecc8dbb9b8310e7c2882c800dc6d2d06859b9c85baa67f4cad53aaf9f0cbdd936f4b1c09f549eed8ae33c1c1258d7b6b1648cde
  languageName: node
  linkType: hard

"buffer-xor@npm:^1.0.3":
  version: 1.0.3
  resolution: "buffer-xor@npm:1.0.3"
  checksum: 4a63d48b5117c7eda896d81cd3582d9707329b07c97a14b0ece2edc6e64220ea7ea17c94b295e8c2cb7b9f8291e2b079f9096be8ac14be238420a43e06ec66e2
  languageName: node
  linkType: hard

"buffer@npm:^5.0.5, buffer@npm:^5.5.0, buffer@npm:^5.6.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 997434d3c6e3b39e0be479a80288875f71cd1c07d75a3855e6f08ef848a3c966023f79534e22e415ff3a5112708ce06127277ab20e527146d55c84566405c7c6
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: b6bc68237ebf29bdacae48ce60e5e28fc53ae886301f2ad9496618efac49427ed79096750033e7eab1897a4f26ae374ace49106a5758f38fb70c78c9fda2c3b1
  languageName: node
  linkType: hard

"bufferutil@npm:^4.0.1":
  version: 4.0.8
  resolution: "bufferutil@npm:4.0.8"
  dependencies:
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.3.0"
  checksum: d9337badc960a19d5a031db5de47159d7d8a11b6bab399bdfbf464ffa9ecd2972fef19bb61a7d2827e0c55f912c20713e12343386b86cb013f2b99c2324ab6a3
  languageName: node
  linkType: hard

"bufio@npm:^1.0.7":
  version: 1.2.1
  resolution: "bufio@npm:1.2.1"
  checksum: c8920ee0d765eb97d218643705346c3360ae6227414df7b3f345932531b85d18fe385d0740e1bcda6225fa082a179910679f444a6de5aec7aecd176a01e40573
  languageName: node
  linkType: hard

"bundle-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "bundle-name@npm:3.0.0"
  dependencies:
    run-applescript: "npm:^5.0.0"
  checksum: edf2b1fbe6096ed32e7566947ace2ea937ee427391744d7510a2880c4b9a5b3543d3f6c551236a29e5c87d3195f8e2912516290e638c15bcbede7b37cc375615
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: a10abf2ba70c784471d6b4f58778c0beeb2b5d405148e66affa91f23a9f13d07603d0a0354667310ae1d6dc141474ffd44e2a074be0f6e2254edb8fc21445388
  languageName: node
  linkType: hard

"cacache@npm:^18.0.0":
  version: 18.0.1
  resolution: "cacache@npm:18.0.1"
  dependencies:
    "@npmcli/fs": "npm:^3.1.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^4.0.0"
    ssri: "npm:^10.0.0"
    tar: "npm:^6.1.11"
    unique-filename: "npm:^3.0.0"
  checksum: aecafd368fbfb2fc0cda1f2f831fe5a1d8161d2121317c92ac089bcd985085e8a588e810b4471e69946f91c6d2661849400e963231563c519aa1e3dac2cf6187
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^5.0.3":
  version: 5.0.4
  resolution: "cacheable-lookup@npm:5.0.4"
  checksum: 618a8b3eea314060e74cb3285a6154e8343c244a34235acf91cfe626ee0705c24e3cd11e4b1a7b3900bd749ee203ae65afe13adf610c8ab173e99d4a208faf75
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^6.0.4":
  version: 6.1.0
  resolution: "cacheable-lookup@npm:6.1.0"
  checksum: 9b37d31fba27ff244254294814dfdad69e3d257cb283932f58823141de5043a46d35339fa81ec40fdbb5d76d1578324258995f41a4fd37ed05d4e9b54823802e
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^7.0.0":
  version: 7.0.0
  resolution: "cacheable-lookup@npm:7.0.0"
  checksum: 69ea78cd9f16ad38120372e71ba98b64acecd95bbcbcdad811f857dc192bad81ace021f8def012ce19178583db8d46afd1a00b3e8c88527e978e049edbc23252
  languageName: node
  linkType: hard

"cacheable-request@npm:^10.2.8":
  version: 10.2.14
  resolution: "cacheable-request@npm:10.2.14"
  dependencies:
    "@types/http-cache-semantics": "npm:^4.0.2"
    get-stream: "npm:^6.0.1"
    http-cache-semantics: "npm:^4.1.1"
    keyv: "npm:^4.5.3"
    mimic-response: "npm:^4.0.0"
    normalize-url: "npm:^8.0.0"
    responselike: "npm:^3.0.0"
  checksum: 102f454ac68eb66f99a709c5cf65e90ed89f1b9269752578d5a08590b3986c3ea47a5d9dff208fe7b65855a29da129a2f23321b88490106898e0ba70b807c912
  languageName: node
  linkType: hard

"cacheable-request@npm:^7.0.2":
  version: 7.0.4
  resolution: "cacheable-request@npm:7.0.4"
  dependencies:
    clone-response: "npm:^1.0.2"
    get-stream: "npm:^5.1.0"
    http-cache-semantics: "npm:^4.0.0"
    keyv: "npm:^4.0.0"
    lowercase-keys: "npm:^2.0.0"
    normalize-url: "npm:^6.0.1"
    responselike: "npm:^2.0.0"
  checksum: 0f4f2001260ecca78b9f64fc8245e6b5a5dcde24ea53006daab71f5e0e1338095aa1512ec099c4f9895a9e5acfac9da423cb7c079e131485891e9214aca46c41
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0, call-bind@npm:^1.0.2, call-bind@npm:^1.0.4, call-bind@npm:^1.0.5":
  version: 1.0.5
  resolution: "call-bind@npm:1.0.5"
  dependencies:
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.1"
    set-function-length: "npm:^1.1.1"
  checksum: 246d44db6ef9bbd418828dbd5337f80b46be4398d522eded015f31554cbb2ea33025b0203b75c7ab05a1a255b56ef218880cca1743e4121e306729f9e414da39
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:^6.0.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001565":
  version: 1.0.30001570
  resolution: "caniuse-lite@npm:1.0.30001570"
  checksum: a9b939e003dd70580cc18bce54627af84f298af7c774415c8d6c99871e7cee13bd8278b67955a979cd338369c73e8821a10b37e607d1fff2fbc8ff92fc489653
  languageName: node
  linkType: hard

"case@npm:^1.6.3":
  version: 1.6.3
  resolution: "case@npm:1.6.3"
  checksum: 2fc1df75bbb4118339e06141b9a54aba95cc62460ac92730290144fbec6b6a04f5bf7abf6a6486a1338f5821bd184402f216cec8cea0472451759c27e20fc332
  languageName: node
  linkType: hard

"caseless@npm:~0.12.0":
  version: 0.12.0
  resolution: "caseless@npm:0.12.0"
  checksum: ea1efdf430975fdbac3505cdd21007f7ac5aa29b6d4d1c091f965853cd1bf87e4b08ea07b31a6d688b038872b7cdf0589d9262d59c699d199585daad052aeb20
  languageName: node
  linkType: hard

"catering@npm:^2.1.0, catering@npm:^2.1.1":
  version: 2.1.1
  resolution: "catering@npm:2.1.1"
  checksum: 4669c9fa5f3a73273535fb458a964d8aba12dc5102d8487049cf03623bef3cdff4b5d9f92ff04c00f1001057a7cc7df6e700752ac622c2a7baf7bcff34166683
  languageName: node
  linkType: hard

"chai@npm:^4.3.4":
  version: 4.3.10
  resolution: "chai@npm:4.3.10"
  dependencies:
    assertion-error: "npm:^1.1.0"
    check-error: "npm:^1.0.3"
    deep-eql: "npm:^4.1.3"
    get-func-name: "npm:^2.0.2"
    loupe: "npm:^2.3.6"
    pathval: "npm:^1.1.1"
    type-detect: "npm:^4.0.8"
  checksum: 9e545fd60f5efee4f06f7ad62f7b1b142932b08fbb3454db69defd511e7c58771ce51843764212da1e129b2c9d1b029fbf5f98da030fe67a95a0853e8679524f
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 3d1d103433166f6bfe82ac75724951b33769675252d8417317363ef9d54699b7c3b2d46671b772b893a8e50c3ece70c4b933c73c01e81bc60ea4df9b55afa303
  languageName: node
  linkType: hard

"chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"check-error@npm:^1.0.3":
  version: 1.0.3
  resolution: "check-error@npm:1.0.3"
  dependencies:
    get-func-name: "npm:^2.0.2"
  checksum: e2131025cf059b21080f4813e55b3c480419256914601750b0fee3bd9b2b8315b531e551ef12560419b8b6d92a3636511322752b1ce905703239e7cc451b6399
  languageName: node
  linkType: hard

"checkpoint-store@npm:^1.1.0":
  version: 1.1.0
  resolution: "checkpoint-store@npm:1.1.0"
  dependencies:
    functional-red-black-tree: "npm:^1.0.1"
  checksum: 94e921ccb222c7970615e8b2bcd956dbd52f15a1c397af0447dbdef8ecd32ffe342e394d39e55f2912278a460f3736de777b5b57a5baf229c0a6bd04d2465511
  languageName: node
  linkType: hard

"chokidar@npm:3.5.3, chokidar@npm:^3.4.0, chokidar@npm:^3.5.2":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 863e3ff78ee7a4a24513d2a416856e84c8e4f5e60efbe03e8ab791af1a183f569b62fc6f6b8044e2804966cb81277ddbbc1dc374fba3265bd609ea8efd62f5b3
  languageName: node
  linkType: hard

"chownr@npm:^1.1.4":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: 115648f8eb38bac5e41c3857f3e663f9c39ed6480d1349977c4d96c95a47266fcacc5a5aabf3cb6c481e22d72f41992827db47301851766c4fd77ac21a4f081d
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 3b374666a85ea3ca43fa49aa3a048d21c9b475c96eb13c133505d2324e7ae5efd6a454f41efe46a152269e9b6a00c9edbe63ec7fa1921957165aae16625acd67
  languageName: node
  linkType: hard

"cids@npm:^0.7.1":
  version: 0.7.5
  resolution: "cids@npm:0.7.5"
  dependencies:
    buffer: "npm:^5.5.0"
    class-is: "npm:^1.1.0"
    multibase: "npm:~0.6.0"
    multicodec: "npm:^1.0.0"
    multihashes: "npm:~0.4.15"
  checksum: b916b0787e238dd9f84fb5e155333cadf07fd7ad34ea8dbd47f98bb618eecc9c70760767c0966d0eae73050c4fa6080fdc387e515565b009d2126253c7775fac
  languageName: node
  linkType: hard

"cipher-base@npm:^1.0.0, cipher-base@npm:^1.0.1, cipher-base@npm:^1.0.3":
  version: 1.0.4
  resolution: "cipher-base@npm:1.0.4"
  dependencies:
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 3d5d6652ca499c3f7c5d7fdc2932a357ec1e5aa84f2ad766d850efd42e89753c97b795c3a104a8e7ae35b4e293f5363926913de3bf8181af37067d9d541ca0db
  languageName: node
  linkType: hard

"class-is@npm:^1.1.0":
  version: 1.1.0
  resolution: "class-is@npm:1.1.0"
  checksum: 8147a3e4ce86eb103d78621d665b87e8e33fcb3f54932fdca894b8222820903b43b2f6b4335d8822104702a5dc904c8f187127fdea4e7d48d905488b35c9e6a7
  languageName: node
  linkType: hard

"classic-level@npm:^1.2.0":
  version: 1.3.0
  resolution: "classic-level@npm:1.3.0"
  dependencies:
    abstract-level: "npm:^1.0.2"
    catering: "npm:^2.1.0"
    module-error: "npm:^1.0.1"
    napi-macros: "npm:^2.2.2"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.3.0"
  checksum: b2d07a5932a09bdf0a5b27e662154888c85edd59d75816c7e60960dacadbc7553daa9f5efb58d2575b3eae0540d504ef71d49f438744366626762a0ddefe5cc5
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.0"
    wrap-ansi: "npm:^7.0.0"
  checksum: db858c49af9d59a32d603987e6fddaca2ce716cd4602ba5a2bb3a5af1351eebe82aba8dff3ef3e1b331f7fa9d40ca66e67bdf8e7c327ce0ea959747ead65c0ef
  languageName: node
  linkType: hard

"clone-response@npm:^1.0.2":
  version: 1.0.3
  resolution: "clone-response@npm:1.0.3"
  dependencies:
    mimic-response: "npm:^1.0.0"
  checksum: 4e671cac39b11c60aa8ba0a450657194a5d6504df51bca3fac5b3bd0145c4f8e8464898f87c8406b83232e3bc5cca555f51c1f9c8ac023969ebfbf7f6bdabb2e
  languageName: node
  linkType: hard

"clone@npm:^2.0.0, clone@npm:^2.1.1":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: d9c79efba655f0bf601ab299c57eb54cbaa9860fb011aee9d89ed5ac0d12df1660ab7642fddaabb9a26b7eff0e117d4520512cb70798319ff5d30a111b5310c2
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: ffa319025045f2973919d155f25e7c00d08836b6b33ea2d205418c59bd63a665d713c52d9737a9e0fe467fb194b40fbef1d849bae80d674568ee220a31ef3d10
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.6, combined-stream@npm:^1.0.8, combined-stream@npm:~1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 2e969e637d05d09fa50b02d74c83a1186f6914aae89e6653b62595cc75a221464f884f55f231b8f4df7a49537fba60bdc0427acd2bf324c09a1dbb84837e36e4
  languageName: node
  linkType: hard

"command-exists@npm:^1.2.8":
  version: 1.2.9
  resolution: "command-exists@npm:1.2.9"
  checksum: 46fb3c4d626ca5a9d274f8fe241230817496abc34d12911505370b7411999e183c11adff7078dd8a03ec4cf1391290facda40c6a4faac8203ae38c985eaedd63
  languageName: node
  linkType: hard

"commander@npm:3.0.2":
  version: 3.0.2
  resolution: "commander@npm:3.0.2"
  checksum: f42053569f5954498246783465b39139917a51284bf3361574c9f731fea27a4bd6452dbb1755cc2d923c7b47dfea67930037c7b7e862288f2c397cec9a74da87
  languageName: node
  linkType: hard

"commander@npm:^10.0.0":
  version: 10.0.1
  resolution: "commander@npm:10.0.1"
  checksum: 8799faa84a30da985802e661cc9856adfaee324d4b138413013ef7f087e8d7924b144c30a1f1405475f0909f467665cd9e1ce13270a2f41b141dab0b7a58f3fb
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"config-chain@npm:^1.1.11":
  version: 1.1.13
  resolution: "config-chain@npm:1.1.13"
  dependencies:
    ini: "npm:^1.3.4"
    proto-list: "npm:~1.2.1"
  checksum: 83d22cabf709e7669f6870021c4d552e4fc02e9682702b726be94295f42ce76cfed00f70b2910ce3d6c9465d9758e191e28ad2e72ff4e3331768a90da6c1ef03
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: b7f4ce176e324f19324be69b05bf6f6e411160ac94bc523b782248129eb1ef3be006f6cff431aaea5e337fe5d176ce8830b8c2a1b721626ead8933f0cbe78720
  languageName: node
  linkType: hard

"content-hash@npm:^2.5.2":
  version: 2.5.2
  resolution: "content-hash@npm:2.5.2"
  dependencies:
    cids: "npm:^0.7.1"
    multicodec: "npm:^0.5.5"
    multihashes: "npm:^0.4.15"
  checksum: 7c5d05052aecead40a1bbdd251468a6cc9bf4c48b361b4f138d60e6d876dc3028da6142031578ddc42e44e0024f91cc01b7a539bdb0bf7187e36bec15052e02d
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 585847d98dc7fb8035c02ae2cb76c7a9bd7b25f84c447e5ed55c45c2175e83617c8813871b4ee22f368126af6b2b167df655829007b21aa10302873ea9c62662
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie@npm:0.5.0":
  version: 0.5.0
  resolution: "cookie@npm:0.5.0"
  checksum: aae7911ddc5f444a9025fbd979ad1b5d60191011339bce48e555cb83343d0f98b865ff5c4d71fecdfb8555a5cafdc65632f6fce172f32aaf6936830a883a0380
  languageName: node
  linkType: hard

"cookie@npm:^0.4.1":
  version: 0.4.2
  resolution: "cookie@npm:0.4.2"
  checksum: 2e1de9fdedca54881eab3c0477aeb067f281f3155d9cfee9d28dfb252210d09e85e9d175c0a60689661feb9e35e588515352f2456bc1f8e8db4267e05fd70137
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.33.1":
  version: 3.34.0
  resolution: "core-js-compat@npm:3.34.0"
  dependencies:
    browserslist: "npm:^4.22.2"
  checksum: e29571cc524b4966e331b5876567f13c2b82ed48ac9b02784f3156b29ee1cd82fe3e60052d78b017c429eb61969fd238c22684bb29180908d335266179a29155
  languageName: node
  linkType: hard

"core-util-is@npm:1.0.2":
  version: 1.0.2
  resolution: "core-util-is@npm:1.0.2"
  checksum: d0f7587346b44a1fe6c269267e037dd34b4787191e473c3e685f507229d88561c40eb18872fabfff02977301815d474300b7bfbd15396c13c5377393f7e87ec3
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cors@npm:^2.8.1":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: "npm:^4"
    vary: "npm:^1"
  checksum: 66e88e08edee7cbce9d92b4d28a2028c88772a4c73e02f143ed8ca76789f9b59444eed6b1c167139e76fa662998c151322720093ba229f9941365ada5a6fc2c6
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.0.0":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: "npm:^3.3.0"
    js-yaml: "npm:^4.1.0"
    parse-json: "npm:^5.2.0"
    path-type: "npm:^4.0.0"
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 91d082baca0f33b1c085bf010f9ded4af43cbedacba8821da0fb5667184d0a848addc52c31fadd080007f904a555319c238cf5f4c03e6d58ece2e4876b2e73d6
  languageName: node
  linkType: hard

"crc-32@npm:^1.2.0":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: 824f696a5baaf617809aa9cd033313c8f94f12d15ebffa69f10202480396be44aef9831d900ab291638a8022ed91c360696dd5b1ba691eb3f34e60be8835b7c3
  languageName: node
  linkType: hard

"create-hash@npm:^1.1.0, create-hash@npm:^1.1.2, create-hash@npm:^1.2.0":
  version: 1.2.0
  resolution: "create-hash@npm:1.2.0"
  dependencies:
    cipher-base: "npm:^1.0.1"
    inherits: "npm:^2.0.1"
    md5.js: "npm:^1.3.4"
    ripemd160: "npm:^2.0.1"
    sha.js: "npm:^2.4.0"
  checksum: 3cfef32043b47a8999602af9bcd74966db6971dd3eb828d1a479f3a44d7f58e38c1caf34aa21a01941cc8d9e1a841738a732f200f00ea155f8a8835133d2e7bc
  languageName: node
  linkType: hard

"create-hmac@npm:^1.1.4, create-hmac@npm:^1.1.7":
  version: 1.1.7
  resolution: "create-hmac@npm:1.1.7"
  dependencies:
    cipher-base: "npm:^1.0.3"
    create-hash: "npm:^1.1.0"
    inherits: "npm:^2.0.1"
    ripemd160: "npm:^2.0.0"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 2b26769f87e99ef72150bf99d1439d69272b2e510e23a2b8daf4e93e2412f4842504237d726044fa797cb20ee0ec8bee78d414b11f2d7ca93299185c93df0dae
  languageName: node
  linkType: hard

"cross-fetch@npm:^2.1.0":
  version: 2.2.6
  resolution: "cross-fetch@npm:2.2.6"
  dependencies:
    node-fetch: "npm:^2.6.7"
    whatwg-fetch: "npm:^2.0.4"
  checksum: ebddaf11bc2dd267650b8a803c98422097de436a366abd498d9a21832fcb0edca59610ee6e30eff65e004fe3689e8b418ce6d52fe503824b59909df8cbe27e10
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.4":
  version: 3.1.8
  resolution: "cross-fetch@npm:3.1.8"
  dependencies:
    node-fetch: "npm:^2.6.12"
  checksum: ac8c4ca87d2ac0e17a19b6a293a67ee8934881aee5ec9a5a8323c30e9a9a60a0f5291d3c0d633ec2a2f970cbc60978d628804dfaf03add92d7e720b6d37f392c
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: e1a13869d2f57d974de0d9ef7acbf69dc6937db20b918525a01dacb5032129bd552d290d886d981e99f1b624cb03657084cc87bd40f115c07ecf376821c729ce
  languageName: node
  linkType: hard

"d@npm:1, d@npm:^1.0.1":
  version: 1.0.1
  resolution: "d@npm:1.0.1"
  dependencies:
    es5-ext: "npm:^0.10.50"
    type: "npm:^1.0.1"
  checksum: 1296e3f92e646895681c1cb564abd0eb23c29db7d62c5120a279e84e98915499a477808e9580760f09e3744c0ed7ac8f7cff98d096ba9770754f6ef0f1c97983
  languageName: node
  linkType: hard

"dashdash@npm:^1.12.0":
  version: 1.14.1
  resolution: "dashdash@npm:1.14.1"
  dependencies:
    assert-plus: "npm:^1.0.0"
  checksum: 137b287fa021201ce100cef772c8eeeaaafdd2aa7282864022acf3b873021e54cb809e9c060fa164840bf54ff72d00d6e2d8da1ee5a86d7200eeefa1123a8f7f
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: e07005f2b40e04f1bd14a3dd20520e9c4f25f60224cb006ce9d6781732c917964e9ec029fc7f1a151083cd929025ad5133814d4dc624a9aaf020effe4914ed14
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:4.3.4, debug@npm:^4.1.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 0073c3bcbd9cb7d71dd5f6b55be8701af42df3e56e911186dfa46fac3a5b9eb7ce7f377dd1d3be6db8977221f8eb333d945216f645cf56f6b688cd484837d255
  languageName: node
  linkType: hard

"decamelize@npm:^4.0.0":
  version: 4.0.0
  resolution: "decamelize@npm:4.0.0"
  checksum: b7d09b82652c39eead4d6678bb578e3bebd848add894b76d0f6b395bc45b2d692fb88d977e7cfb93c4ed6c119b05a1347cef261174916c2e75c0a8ca57da1809
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.0":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 17a0e5fa400bf9ea84432226e252aa7b5e72793e16bf80b907c99b46a799aeacc139ec20ea57121e50c7bd875a1a4365928f884e92abf02e21a5a13790a0f33e
  languageName: node
  linkType: hard

"decompress-response@npm:^3.3.0":
  version: 3.3.0
  resolution: "decompress-response@npm:3.3.0"
  dependencies:
    mimic-response: "npm:^1.0.0"
  checksum: 952552ac3bd7de2fc18015086b09468645c9638d98a551305e485230ada278c039c91116e946d07894b39ee53c0f0d5b6473f25a224029344354513b412d7380
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: "npm:^3.1.0"
  checksum: d377cf47e02d805e283866c3f50d3d21578b779731e8c5072d6ce8c13cc31493db1c2f6784da9d1d5250822120cefa44f1deab112d5981015f2e17444b763812
  languageName: node
  linkType: hard

"deep-eql@npm:^4.1.3":
  version: 4.1.3
  resolution: "deep-eql@npm:4.1.3"
  dependencies:
    type-detect: "npm:^4.0.0"
  checksum: 12ce93ae63de187e77b076d3d51bfc28b11f98910a22c18714cce112791195e86a94f97788180994614b14562a86c9763f67c69f785e4586f806b5df39bf9301
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"default-browser-id@npm:^3.0.0":
  version: 3.0.0
  resolution: "default-browser-id@npm:3.0.0"
  dependencies:
    bplist-parser: "npm:^0.2.0"
    untildify: "npm:^4.0.0"
  checksum: 279c7ad492542e5556336b6c254a4eaf31b2c63a5433265655ae6e47301197b6cfb15c595a6fdc6463b2ff8e1a1a1ed3cba56038a60e1527ba4ab1628c6b9941
  languageName: node
  linkType: hard

"default-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "default-browser@npm:4.0.0"
  dependencies:
    bundle-name: "npm:^3.0.0"
    default-browser-id: "npm:^3.0.0"
    execa: "npm:^7.1.1"
    titleize: "npm:^3.0.0"
  checksum: 40c5af984799042b140300be5639c9742599bda76dc9eba5ac9ad5943c83dd36cebc4471eafcfddf8e0ec817166d5ba89d56f08e66a126c7c7908a179cead1a7
  languageName: node
  linkType: hard

"defer-to-connect@npm:^2.0.0, defer-to-connect@npm:^2.0.1":
  version: 2.0.1
  resolution: "defer-to-connect@npm:2.0.1"
  checksum: 8a9b50d2f25446c0bfefb55a48e90afd58f85b21bcf78e9207cd7b804354f6409032a1705c2491686e202e64fc05f147aa5aa45f9aa82627563f045937f5791b
  languageName: node
  linkType: hard

"deferred-leveldown@npm:~1.2.1":
  version: 1.2.2
  resolution: "deferred-leveldown@npm:1.2.2"
  dependencies:
    abstract-leveldown: "npm:~2.6.0"
  checksum: 17a79955e8191264c79422235dc5eea46fa1b5be43c06d50bfb149679721ec389168198ac2edf6635bd02372db19c1b0db5154d502b2ee93c33f0ad9644597be
  languageName: node
  linkType: hard

"define-data-property@npm:^1.1.1":
  version: 1.1.1
  resolution: "define-data-property@npm:1.1.1"
  dependencies:
    get-intrinsic: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
  checksum: 5573c8df96b5857408cad64d9b91b69152e305ce4b06218e5f49b59c6cafdbb90a8bd8a0bb83c7bc67a8d479c04aa697063c9bc28d849b7282f9327586d6bc7b
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: f28421cf9ee86eecaf5f3b8fe875f13d7009c2625e97645bfff7a2a49aca678270b86c39f9c32939e5ca7ab96b551377ed4139558c795e076774287ad3af1aa4
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: c0c8ff36079ce5ada64f46cc9d6fd47ebcf38241105b6e0c98f412e8ad91f084bcf906ff644cc3a4bd876ca27a62accb8b0fff72ea6ed1a414b89d8506f4a5ca
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"detect-indent@npm:^7.0.1":
  version: 7.0.1
  resolution: "detect-indent@npm:7.0.1"
  checksum: cbf3f0b1c3c881934ca94428e1179b26ab2a587e0d719031d37a67fb506d49d067de54ff057cb1e772e75975fed5155c01cd4518306fee60988b1486e3fc7768
  languageName: node
  linkType: hard

"detect-newline@npm:^4.0.0":
  version: 4.0.1
  resolution: "detect-newline@npm:4.0.1"
  checksum: 0409ecdfb93419591ccff24fccfe2ddddad29b66637d1ed898872125b25af05014fdeedc9306339577060f69f59fe6e9830cdd80948597f136dfbffefa60599c
  languageName: node
  linkType: hard

"diff@npm:5.0.0":
  version: 5.0.0
  resolution: "diff@npm:5.0.0"
  checksum: 4a179a75b17cbb420eb9145be913f9ddb34b47cb2ba4301e80ae745122826a468f02ca8f5e56945958de26ace594899c8381acb6659c88e7803ef078b53d690c
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"dom-walk@npm:^0.1.0":
  version: 0.1.2
  resolution: "dom-walk@npm:0.1.2"
  checksum: 19eb0ce9c6de39d5e231530685248545d9cd2bd97b2cb3486e0bfc0f2a393a9addddfd5557463a932b52fdfcf68ad2a619020cd2c74a5fe46fbecaa8e80872f3
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"ecc-jsbn@npm:~0.1.1":
  version: 0.1.2
  resolution: "ecc-jsbn@npm:0.1.2"
  dependencies:
    jsbn: "npm:~0.1.0"
    safer-buffer: "npm:^2.1.0"
  checksum: d43591f2396196266e186e6d6928038cc11c76c3699a912cb9c13757060f7bbc7f17f47c4cb16168cdeacffc7965aef021142577e646fb3cb88810c15173eb57
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.601":
  version: 1.4.612
  resolution: "electron-to-chromium@npm:1.4.612"
  checksum: 3c7c09d778e68aa161637a934dc1466fb87385ab3b964fd96399dfb8701c7fbe1ab1576cc25f02a2f8c618b2c4d8800379fdd29f121735e59277e67a4e783fab
  languageName: node
  linkType: hard

"elliptic@npm:6.5.4, elliptic@npm:^6.4.0, elliptic@npm:^6.5.2, elliptic@npm:^6.5.4":
  version: 6.5.4
  resolution: "elliptic@npm:6.5.4"
  dependencies:
    bn.js: "npm:^4.11.9"
    brorand: "npm:^1.1.0"
    hash.js: "npm:^1.0.0"
    hmac-drbg: "npm:^1.0.1"
    inherits: "npm:^2.0.4"
    minimalistic-assert: "npm:^1.0.1"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 2cd7ff4b69720dbb2ca1ca650b2cf889d1df60c96d4a99d331931e4fe21e45a7f3b8074e86618ca7e56366c4b6258007f234f9d61d9b0c87bbbc8ea990b99e94
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encode-utf8@npm:^1.0.2":
  version: 1.0.3
  resolution: "encode-utf8@npm:1.0.3"
  checksum: 0204c37cda21bf19bb8f87f7ec6c89a23d43488c2ef1e5cfa40b64ee9568e63e15dc323fa7f50a491e2c6d33843a6b409f6de09afbf6cf371cb8da596cc64b44
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"enquirer@npm:^2.3.0, enquirer@npm:^2.3.6":
  version: 2.4.1
  resolution: "enquirer@npm:2.4.1"
  dependencies:
    ansi-colors: "npm:^4.1.1"
    strip-ansi: "npm:^6.0.1"
  checksum: b3726486cd98f0d458a851a03326a2a5dd4d84f37ff94ff2a2960c915e0fc865865da3b78f0877dc36ac5c1189069eca603e82ec63d5bc6b0dd9985bf6426d7a
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"errno@npm:~0.1.1":
  version: 0.1.8
  resolution: "errno@npm:0.1.8"
  dependencies:
    prr: "npm:~1.0.1"
  bin:
    errno: cli.js
  checksum: 93076ed11bedb8f0389cbefcbdd3445f66443159439dccbaac89a053428ad92147676736235d275612dc0296d3f9a7e6b7177ed78a566b6cd15dacd4fa0d5888
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: d547740aa29c34e753fb6fed2c5de81802438529c12b3673bd37b6bb1fe49b9b7abdc3c11e6062fe625d8a296b3cf769a80f878865e25e685f787763eede3ffb
  languageName: node
  linkType: hard

"es5-ext@npm:^0.10.35, es5-ext@npm:^0.10.50":
  version: 0.10.62
  resolution: "es5-ext@npm:0.10.62"
  dependencies:
    es6-iterator: "npm:^2.0.3"
    es6-symbol: "npm:^3.1.3"
    next-tick: "npm:^1.1.0"
  checksum: 3f6a3bcdb7ff82aaf65265799729828023c687a2645da04005b8f1dc6676a0c41fd06571b2517f89dcf143e0268d3d9ef0fdfd536ab74580083204c688d6fb45
  languageName: node
  linkType: hard

"es6-iterator@npm:^2.0.3":
  version: 2.0.3
  resolution: "es6-iterator@npm:2.0.3"
  dependencies:
    d: "npm:1"
    es5-ext: "npm:^0.10.35"
    es6-symbol: "npm:^3.1.1"
  checksum: dbadecf3d0e467692815c2b438dfa99e5a97cbbecf4a58720adcb467a04220e0e36282399ba297911fd472c50ae4158fffba7ed0b7d4273fe322b69d03f9e3a5
  languageName: node
  linkType: hard

"es6-promise@npm:^4.2.8":
  version: 4.2.8
  resolution: "es6-promise@npm:4.2.8"
  checksum: b250c55523c496c43c9216c2646e58ec182b819e036fe5eb8d83fa16f044ecc6b8dcefc88ace2097be3d3c4d02b6aa8eeae1a66deeaf13e7bee905ebabb350a3
  languageName: node
  linkType: hard

"es6-symbol@npm:^3.1.1, es6-symbol@npm:^3.1.3":
  version: 3.1.3
  resolution: "es6-symbol@npm:3.1.3"
  dependencies:
    d: "npm:^1.0.1"
    ext: "npm:^1.1.2"
  checksum: b404e5ecae1a076058aa2ba2568d87e2cb4490cb1130784b84e7b4c09c570b487d4f58ed685a08db8d350bd4916500dd3d623b26e6b3520841d30d2ebb152f8d
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: afa618e73362576b63f6ca83c975456621095a1ed42ff068174e3f5cea48afc422814dda548c96e6ebb5333e7265140c7292abcc81bbd6ccb1757d50d3a4e182
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"eth-block-tracker@npm:^4.4.2":
  version: 4.4.3
  resolution: "eth-block-tracker@npm:4.4.3"
  dependencies:
    "@babel/plugin-transform-runtime": "npm:^7.5.5"
    "@babel/runtime": "npm:^7.5.5"
    eth-query: "npm:^2.1.0"
    json-rpc-random-id: "npm:^1.0.1"
    pify: "npm:^3.0.0"
    safe-event-emitter: "npm:^1.0.1"
  checksum: 8f59b71cbc787dcea773ada8acc20fdf3f46f24e2eed1b0236a5ec3de64536e3a1fd18a135475784e6ae94685d9ab5c1e7e8f9d17f114484223b63d2ed0e47a4
  languageName: node
  linkType: hard

"eth-ens-namehash@npm:2.0.8":
  version: 2.0.8
  resolution: "eth-ens-namehash@npm:2.0.8"
  dependencies:
    idna-uts46-hx: "npm:^2.3.1"
    js-sha3: "npm:^0.5.7"
  checksum: 098c04378b0b998191b4bcd2f1a59be976946bbb80cea7bc2a6d1df3a035e061b2fd120b16bf41558c4beb2dd846433742058b091b20195e4b0e1fc64b67979f
  languageName: node
  linkType: hard

"eth-json-rpc-filters@npm:^4.2.1":
  version: 4.2.2
  resolution: "eth-json-rpc-filters@npm:4.2.2"
  dependencies:
    "@metamask/safe-event-emitter": "npm:^2.0.0"
    async-mutex: "npm:^0.2.6"
    eth-json-rpc-middleware: "npm:^6.0.0"
    eth-query: "npm:^2.1.2"
    json-rpc-engine: "npm:^6.1.0"
    pify: "npm:^5.0.0"
  checksum: 76de0a307be7e22e54ff5d71bf7effb78c38c8c2d99973df0bcc517da404d5dececb0622531e99cebcf906a180fdf00b33fdf8545d9ef2d35418b778905dc479
  languageName: node
  linkType: hard

"eth-json-rpc-infura@npm:^5.1.0":
  version: 5.1.0
  resolution: "eth-json-rpc-infura@npm:5.1.0"
  dependencies:
    eth-json-rpc-middleware: "npm:^6.0.0"
    eth-rpc-errors: "npm:^3.0.0"
    json-rpc-engine: "npm:^5.3.0"
    node-fetch: "npm:^2.6.0"
  checksum: 29712d77741b6bc94634d32286095e30e65e793034d1ba80fa14719ddb1a85a34cc8eddc36f0bbe3f9aaa841b80217e5347bb919004068749e0e945eff637a98
  languageName: node
  linkType: hard

"eth-json-rpc-middleware@npm:^6.0.0":
  version: 6.0.0
  resolution: "eth-json-rpc-middleware@npm:6.0.0"
  dependencies:
    btoa: "npm:^1.2.1"
    clone: "npm:^2.1.1"
    eth-query: "npm:^2.1.2"
    eth-rpc-errors: "npm:^3.0.0"
    eth-sig-util: "npm:^1.4.2"
    ethereumjs-util: "npm:^5.1.2"
    json-rpc-engine: "npm:^5.3.0"
    json-stable-stringify: "npm:^1.0.1"
    node-fetch: "npm:^2.6.1"
    pify: "npm:^3.0.0"
    safe-event-emitter: "npm:^1.0.1"
  checksum: 522bbd472d785d2f8754c8a8d93a59385f377fe069025a9932fec29f02dd0d17c6ecc2d468058a18ee52ccb48528f2898560db286074ecfb974816a5f2b02d4b
  languageName: node
  linkType: hard

"eth-lib@npm:0.2.8":
  version: 0.2.8
  resolution: "eth-lib@npm:0.2.8"
  dependencies:
    bn.js: "npm:^4.11.6"
    elliptic: "npm:^6.4.0"
    xhr-request-promise: "npm:^0.1.2"
  checksum: 85a6f1673c7106252864fdf6c86973d6bfdf454b238ee8d07d8f642599fa9f390129b6fbd060742a5be7c197be924951535a0c0ebb3e912cfd9f2130b64f74ce
  languageName: node
  linkType: hard

"eth-lib@npm:^0.1.26":
  version: 0.1.29
  resolution: "eth-lib@npm:0.1.29"
  dependencies:
    bn.js: "npm:^4.11.6"
    elliptic: "npm:^6.4.0"
    nano-json-stream-parser: "npm:^0.1.2"
    servify: "npm:^0.1.12"
    ws: "npm:^3.0.0"
    xhr-request-promise: "npm:^0.1.2"
  checksum: ee4fcd8400fad0b637c25bd0a4483a54c986b78ac6c4d7fd2a5df12b41468abfa50a66684e315e16894b870d2fcf5d2273a81f429f89c460b275bf4477365f60
  languageName: node
  linkType: hard

"eth-query@npm:^2.1.0, eth-query@npm:^2.1.2":
  version: 2.1.2
  resolution: "eth-query@npm:2.1.2"
  dependencies:
    json-rpc-random-id: "npm:^1.0.0"
    xtend: "npm:^4.0.1"
  checksum: af4f3575b8315f8156a83a24e850881053748aca97e4aee12dd6645ab56f0985c7000a5c45ccf315702f3e532f0c6464e03f4aba294c658dee89f5e5d1b86702
  languageName: node
  linkType: hard

"eth-rpc-errors@npm:^3.0.0":
  version: 3.0.0
  resolution: "eth-rpc-errors@npm:3.0.0"
  dependencies:
    fast-safe-stringify: "npm:^2.0.6"
  checksum: bf11fccfecb2a2438c2cd2da0aa09b5b7f1192007a0a3ab819684865141dbc0b2789815a59cbd8948161a16d042fd4476c1bd1aba5ba01be63c83dc84391aebb
  languageName: node
  linkType: hard

"eth-rpc-errors@npm:^4.0.2":
  version: 4.0.3
  resolution: "eth-rpc-errors@npm:4.0.3"
  dependencies:
    fast-safe-stringify: "npm:^2.0.6"
  checksum: 47ce14170eabaee51ab1cc7e643bb3ef96ee6b15c6404806aedcd51750e00ae0b1a12c37785b180679b8d452b6dd44a0240bb018d01fa73efc85fcfa808b35a7
  languageName: node
  linkType: hard

"eth-sig-util@npm:^1.4.2":
  version: 1.4.2
  resolution: "eth-sig-util@npm:1.4.2"
  dependencies:
    ethereumjs-abi: "git+https://github.com/ethereumjs/ethereumjs-abi.git"
    ethereumjs-util: "npm:^5.1.1"
  checksum: f92b85cb5560c40b2dace2b683b9dee1e9e7eed1f1a602c59bd3f33b90f67356c64ca54dd6bea45dd9ea59da2167ff76cc012f3e73d89e09a1f7be0902680fef
  languageName: node
  linkType: hard

"ethereum-bloom-filters@npm:^1.0.6":
  version: 1.0.10
  resolution: "ethereum-bloom-filters@npm:1.0.10"
  dependencies:
    js-sha3: "npm:^0.8.0"
  checksum: dc4191c5d810db864ace106886f340b541bf03f1ad3249459ac630cab9c191f1e45c03e935887cca903cca884326e3ac97acfef0a083c7e1a004108f5991f9ba
  languageName: node
  linkType: hard

"ethereum-common@npm:0.2.0":
  version: 0.2.0
  resolution: "ethereum-common@npm:0.2.0"
  checksum: a5dad354bcfd01651b4b0f7a2cd7a8165154b19d3daa8023c3328bcebe209be0311f552cf100e394d8a36869485ed90064047728f60c7eef7689c939b5c40681
  languageName: node
  linkType: hard

"ethereum-common@npm:^0.0.18":
  version: 0.0.18
  resolution: "ethereum-common@npm:0.0.18"
  checksum: 8348d686e7aea9b1a3aef6200a38f46873636130ed99dd0220585f27bca1a5f51b47b7a735eb532471277ab20aedd1f0129530811af61d88dc8379ef650d20b3
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:0.1.3, ethereum-cryptography@npm:^0.1.3":
  version: 0.1.3
  resolution: "ethereum-cryptography@npm:0.1.3"
  dependencies:
    "@types/pbkdf2": "npm:^3.0.0"
    "@types/secp256k1": "npm:^4.0.1"
    blakejs: "npm:^1.1.0"
    browserify-aes: "npm:^1.2.0"
    bs58check: "npm:^2.1.2"
    create-hash: "npm:^1.2.0"
    create-hmac: "npm:^1.1.7"
    hash.js: "npm:^1.1.7"
    keccak: "npm:^3.0.0"
    pbkdf2: "npm:^3.0.17"
    randombytes: "npm:^2.1.0"
    safe-buffer: "npm:^5.1.2"
    scrypt-js: "npm:^3.0.0"
    secp256k1: "npm:^4.0.1"
    setimmediate: "npm:^1.0.5"
  checksum: 975e476782746acd97d5b37366801ae622a52fb31e5d83f600804be230a61ef7b9d289dcecd9c308fb441967caf3a6e3768dd7c8add6441fcc60c398175d5a96
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:1.1.2":
  version: 1.1.2
  resolution: "ethereum-cryptography@npm:1.1.2"
  dependencies:
    "@noble/hashes": "npm:1.1.2"
    "@noble/secp256k1": "npm:1.6.3"
    "@scure/bip32": "npm:1.1.0"
    "@scure/bip39": "npm:1.1.0"
  checksum: abf9288086002a697e0ee0077d77d001c8e1306fa53ea8d7901f9744786f47d073caa6c266bd5b25a283a5c0fbc8beed9fa9cd90d842dc51339e6748aa1ab46a
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^1.0.3":
  version: 1.2.0
  resolution: "ethereum-cryptography@npm:1.2.0"
  dependencies:
    "@noble/hashes": "npm:1.2.0"
    "@noble/secp256k1": "npm:1.7.1"
    "@scure/bip32": "npm:1.1.5"
    "@scure/bip39": "npm:1.1.1"
  checksum: e8b2ab91e0237ed83a6e6ab1aa2a61ee081dea137ac994c7daa935b0b620e866f70e2ac7eb2fb8db2dec044fe22283d2bf940598417e4dccd15a2b704a817a1b
  languageName: node
  linkType: hard

"ethereum-protocol@npm:^1.0.1":
  version: 1.0.1
  resolution: "ethereum-protocol@npm:1.0.1"
  checksum: bec2ea6c280dc7cb0771751e71bc7ffd0318d0d1577e57db27866a8140671d8cc7315f344383ad47ee54cd1f0b31e69d2c1e979b6999b229dc48397f3b89b957
  languageName: node
  linkType: hard

"ethereumjs-abi@npm:0.6.8":
  version: 0.6.8
  resolution: "ethereumjs-abi@npm:0.6.8"
  dependencies:
    bn.js: "npm:^4.11.8"
    ethereumjs-util: "npm:^6.0.0"
  checksum: d4633ca30048b53c0f900ba5d7d6013ca228822055fbd93f975befc41f5c3054e0fffc27562d78050f164170e546af66c20e9ca1d35e67ea861df07d59a65a91
  languageName: node
  linkType: hard

"ethereumjs-account@npm:^2.0.3":
  version: 2.0.5
  resolution: "ethereumjs-account@npm:2.0.5"
  dependencies:
    ethereumjs-util: "npm:^5.0.0"
    rlp: "npm:^2.0.0"
    safe-buffer: "npm:^5.1.1"
  checksum: 1f734e67378dd55203d6d7e2b307ddeb39fa9a150297636c5e2807aafaa94bc0cd3dfedcb0b964e7e5473d83a35d62dd0475e2b24598fb69d6a92fb57c2a7488
  languageName: node
  linkType: hard

"ethereumjs-block@npm:^1.2.2":
  version: 1.7.1
  resolution: "ethereumjs-block@npm:1.7.1"
  dependencies:
    async: "npm:^2.0.1"
    ethereum-common: "npm:0.2.0"
    ethereumjs-tx: "npm:^1.2.2"
    ethereumjs-util: "npm:^5.0.0"
    merkle-patricia-tree: "npm:^2.1.2"
  checksum: 732423cd2c9f30b0c009783f130f994e104c4f6a88246a417b0d3da80d8e11db58e6692940afe290c02318004945d7396aaff6fba2b959ef800d4e31e3646e77
  languageName: node
  linkType: hard

"ethereumjs-block@npm:~2.2.0":
  version: 2.2.2
  resolution: "ethereumjs-block@npm:2.2.2"
  dependencies:
    async: "npm:^2.0.1"
    ethereumjs-common: "npm:^1.5.0"
    ethereumjs-tx: "npm:^2.1.1"
    ethereumjs-util: "npm:^5.0.0"
    merkle-patricia-tree: "npm:^2.1.2"
  checksum: 5179e91d27acc3614199d8098449f16de7927e14239a996c8e7af6e2f65867048a3125ab79af4bac4f828228d332a0ae5837997b5bb259bd2dab2fe6dde455ac
  languageName: node
  linkType: hard

"ethereumjs-common@npm:^1.1.0, ethereumjs-common@npm:^1.5.0":
  version: 1.5.2
  resolution: "ethereumjs-common@npm:1.5.2"
  checksum: ae24d814ff25a15aaef2ca03d526ce6fe60a5edd17bacb66f7af4ca57910b91e70507f8611a4aac640f92ddcccd2ee7c38e354024c53f5705bc1d489a70f8298
  languageName: node
  linkType: hard

"ethereumjs-tx@npm:^1.2.2":
  version: 1.3.7
  resolution: "ethereumjs-tx@npm:1.3.7"
  dependencies:
    ethereum-common: "npm:^0.0.18"
    ethereumjs-util: "npm:^5.0.0"
  checksum: 967e1047fd0f70ae7f87a4fff41fc7226ff89c6b0a9b925e148deec2dfdccdac1e120d2dc64cf74c9639ae5ac7c51845558eb1d6809146776c8bd39a1065a319
  languageName: node
  linkType: hard

"ethereumjs-tx@npm:^2.1.1":
  version: 2.1.2
  resolution: "ethereumjs-tx@npm:2.1.2"
  dependencies:
    ethereumjs-common: "npm:^1.5.0"
    ethereumjs-util: "npm:^6.0.0"
  checksum: 8a9fcbafe1ec9fdc66248238987f83a78de853f9c1735897741d2bd151f463829a04b351eeb23f2723bf41cf57b35d0761b1424a11aa6cf62a63833aa7181f17
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^5.0.0, ethereumjs-util@npm:^5.1.1, ethereumjs-util@npm:^5.1.2, ethereumjs-util@npm:^5.1.5":
  version: 5.2.1
  resolution: "ethereumjs-util@npm:5.2.1"
  dependencies:
    bn.js: "npm:^4.11.0"
    create-hash: "npm:^1.1.2"
    elliptic: "npm:^6.5.2"
    ethereum-cryptography: "npm:^0.1.3"
    ethjs-util: "npm:^0.1.3"
    rlp: "npm:^2.0.0"
    safe-buffer: "npm:^5.1.1"
  checksum: f3957c7ccb4abe55c894ab1d039ecfbac1ce3025f4adfee61722760440b704f13dd056fdd9eaaa2be1154c17bba152e1c7f93ce91da3d3d1314de9c479003f9b
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^6.0.0, ethereumjs-util@npm:^6.2.1":
  version: 6.2.1
  resolution: "ethereumjs-util@npm:6.2.1"
  dependencies:
    "@types/bn.js": "npm:^4.11.3"
    bn.js: "npm:^4.11.0"
    create-hash: "npm:^1.1.2"
    elliptic: "npm:^6.5.2"
    ethereum-cryptography: "npm:^0.1.3"
    ethjs-util: "npm:0.1.6"
    rlp: "npm:^2.2.3"
  checksum: dedc8a623e21d1864b09c47f28851fc0fca6233cdefa4755a308507822ce75c893bbb2c3ba422109d1247986ec757941718f06574437e41b0d68604108b03fd0
  languageName: node
  linkType: hard

"ethereumjs-util@npm:^7.1.0, ethereumjs-util@npm:^7.1.1, ethereumjs-util@npm:^7.1.2, ethereumjs-util@npm:^7.1.5":
  version: 7.1.5
  resolution: "ethereumjs-util@npm:7.1.5"
  dependencies:
    "@types/bn.js": "npm:^5.1.0"
    bn.js: "npm:^5.1.2"
    create-hash: "npm:^1.1.2"
    ethereum-cryptography: "npm:^0.1.3"
    rlp: "npm:^2.2.4"
  checksum: f28fc1ebb8f35bf9e418f76f51be737d94d603b912c3e014c4e87cd45ccd1b10bdfef764c8f152574b57e9faa260a18773cbc110f9e0a754d6b3730699e54dc9
  languageName: node
  linkType: hard

"ethereumjs-vm@npm:^2.3.4":
  version: 2.6.0
  resolution: "ethereumjs-vm@npm:2.6.0"
  dependencies:
    async: "npm:^2.1.2"
    async-eventemitter: "npm:^0.2.2"
    ethereumjs-account: "npm:^2.0.3"
    ethereumjs-block: "npm:~2.2.0"
    ethereumjs-common: "npm:^1.1.0"
    ethereumjs-util: "npm:^6.0.0"
    fake-merkle-patricia-tree: "npm:^1.0.1"
    functional-red-black-tree: "npm:^1.0.1"
    merkle-patricia-tree: "npm:^2.3.2"
    rustbn.js: "npm:~0.2.0"
    safe-buffer: "npm:^5.1.1"
  checksum: ddf84ce284bf54ec0acb7fa037f0b07fbe4f9c5314a3df5dd9acca9542cbc86107ad9b1614e0061172f38c25ee0ef8ea5d68accf268a4280dbaf68386b4b6fae
  languageName: node
  linkType: hard

"ethers@npm:^5.5.3, ethers@npm:^5.7.1":
  version: 5.7.2
  resolution: "ethers@npm:5.7.2"
  dependencies:
    "@ethersproject/abi": "npm:5.7.0"
    "@ethersproject/abstract-provider": "npm:5.7.0"
    "@ethersproject/abstract-signer": "npm:5.7.0"
    "@ethersproject/address": "npm:5.7.0"
    "@ethersproject/base64": "npm:5.7.0"
    "@ethersproject/basex": "npm:5.7.0"
    "@ethersproject/bignumber": "npm:5.7.0"
    "@ethersproject/bytes": "npm:5.7.0"
    "@ethersproject/constants": "npm:5.7.0"
    "@ethersproject/contracts": "npm:5.7.0"
    "@ethersproject/hash": "npm:5.7.0"
    "@ethersproject/hdnode": "npm:5.7.0"
    "@ethersproject/json-wallets": "npm:5.7.0"
    "@ethersproject/keccak256": "npm:5.7.0"
    "@ethersproject/logger": "npm:5.7.0"
    "@ethersproject/networks": "npm:5.7.1"
    "@ethersproject/pbkdf2": "npm:5.7.0"
    "@ethersproject/properties": "npm:5.7.0"
    "@ethersproject/providers": "npm:5.7.2"
    "@ethersproject/random": "npm:5.7.0"
    "@ethersproject/rlp": "npm:5.7.0"
    "@ethersproject/sha2": "npm:5.7.0"
    "@ethersproject/signing-key": "npm:5.7.0"
    "@ethersproject/solidity": "npm:5.7.0"
    "@ethersproject/strings": "npm:5.7.0"
    "@ethersproject/transactions": "npm:5.7.0"
    "@ethersproject/units": "npm:5.7.0"
    "@ethersproject/wallet": "npm:5.7.0"
    "@ethersproject/web": "npm:5.7.1"
    "@ethersproject/wordlists": "npm:5.7.0"
  checksum: 227dfa88a2547c799c0c3c9e92e5e246dd11342f4b495198b3ae7c942d5bf81d3970fcef3fbac974a9125d62939b2d94f3c0458464e702209b839a8e6e615028
  languageName: node
  linkType: hard

"ethjs-unit@npm:0.1.6":
  version: 0.1.6
  resolution: "ethjs-unit@npm:0.1.6"
  dependencies:
    bn.js: "npm:4.11.6"
    number-to-bn: "npm:1.7.0"
  checksum: 35086cb671806992ec36d5dd43ab67e68ad7a9237e42c0e963f9081c88e40147cda86c1a258b0a3180bf2b7bc1960e607c5bcaefdb2196e0f3564acf73276189
  languageName: node
  linkType: hard

"ethjs-util@npm:0.1.6, ethjs-util@npm:^0.1.3, ethjs-util@npm:^0.1.6":
  version: 0.1.6
  resolution: "ethjs-util@npm:0.1.6"
  dependencies:
    is-hex-prefixed: "npm:1.0.0"
    strip-hex-prefix: "npm:1.0.0"
  checksum: 02e1d37f743a78742651a11be35461dfe8ed653f113d630435aada8036e1e199691c2cfffbbf1e800bfdeb14bb34c7ed69fab5d3c727058c1daf3effc6bf6f69
  languageName: node
  linkType: hard

"eventemitter3@npm:4.0.4":
  version: 4.0.4
  resolution: "eventemitter3@npm:4.0.4"
  checksum: 6a85beb36d7ff2363de71aa19a17c24ecde7a92f706347891befc5901793e41ac847ce9c04c96dc0f5095384890cc737e64f21ed334e75c523d2352056fc6a9e
  languageName: node
  linkType: hard

"events@npm:^3.0.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: a3d47e285e28d324d7180f1e493961a2bbb4cad6412090e4dec114f4db1f5b560c7696ee8e758f55e23913ede856e3689cd3aa9ae13c56b5d8314cd3b3ddd1be
  languageName: node
  linkType: hard

"evp_bytestokey@npm:^1.0.3":
  version: 1.0.3
  resolution: "evp_bytestokey@npm:1.0.3"
  dependencies:
    md5.js: "npm:^1.3.4"
    node-gyp: "npm:latest"
    safe-buffer: "npm:^5.1.1"
  checksum: ad4e1577f1a6b721c7800dcc7c733fe01f6c310732bb5bf2240245c2a5b45a38518b91d8be2c610611623160b9d1c0e91f1ce96d639f8b53e8894625cf20fa45
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 8ada91f2d70f7dff702c861c2c64f21dfdc1525628f3c0454fd6f02fce65f7b958616cbd2b99ca7fa4d474e461a3d363824e91b3eb881705231abbf387470597
  languageName: node
  linkType: hard

"execa@npm:^7.1.1":
  version: 7.2.0
  resolution: "execa@npm:7.2.0"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.1"
    human-signals: "npm:^4.3.0"
    is-stream: "npm:^3.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^5.1.0"
    onetime: "npm:^6.0.0"
    signal-exit: "npm:^3.0.7"
    strip-final-newline: "npm:^3.0.0"
  checksum: 473feff60f9d4dbe799225948de48b5158c1723021d19c4b982afe37bcd111ae84e1b4c9dfe967fae5101b0894b1a62e4dd564a286dfa3e46d7b0cfdbf7fe62b
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 2d9bbb6473de7051f96790d5f9a678f32e60ed0aa70741dc7fdc96fec8d631124ec3374ac144387604f05afff9500f31a1d45bd9eee4cdc2e4f9ad2d9b9d5dbd
  languageName: node
  linkType: hard

"express@npm:^4.14.0":
  version: 4.18.2
  resolution: "express@npm:4.18.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.1"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.5.0"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.2.0"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.1"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.7"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.11.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.18.0"
    serve-static: "npm:1.15.0"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 869ae89ed6ff4bed7b373079dc58e5dddcf2915a2669b36037ff78c99d675ae930e5fe052b35c24f56557d28a023bb1cbe3e2f2fb87eaab96a1cedd7e597809d
  languageName: node
  linkType: hard

"ext@npm:^1.1.2":
  version: 1.7.0
  resolution: "ext@npm:1.7.0"
  dependencies:
    type: "npm:^2.7.2"
  checksum: 666a135980b002df0e75c8ac6c389140cdc59ac953db62770479ee2856d58ce69d2f845e5f2586716350b725400f6945e51e9159573158c39f369984c72dcd84
  languageName: node
  linkType: hard

"extend@npm:~3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 59e89e2dc798ec0f54b36d82f32a27d5f6472c53974f61ca098db5d4648430b725387b53449a34df38fd0392045434426b012f302b3cc049a6500ccf82877e4e
  languageName: node
  linkType: hard

"extsprintf@npm:1.3.0":
  version: 1.3.0
  resolution: "extsprintf@npm:1.3.0"
  checksum: 26967d6c7ecbfb5bc5b7a6c43503dc5fafd9454802037e9fa1665e41f615da4ff5918bd6cb871a3beabed01a31eca1ccd0bdfb41231f50ad50d405a430f78377
  languageName: node
  linkType: hard

"extsprintf@npm:^1.2.0":
  version: 1.4.1
  resolution: "extsprintf@npm:1.4.1"
  checksum: bfd6d55f3c0c04d826fe0213264b383c03f32825af6b1ff777f3f2dc49467e599361993568d75b7b19a8ea1bb08c8e7cd8c3d87d179ced91bb0dcf81ca6938e0
  languageName: node
  linkType: hard

"fake-merkle-patricia-tree@npm:^1.0.1":
  version: 1.0.1
  resolution: "fake-merkle-patricia-tree@npm:1.0.1"
  dependencies:
    checkpoint-store: "npm:^1.1.0"
  checksum: db445809b41a3b99db604cfd943a894a1a10d6e0fe50d4192331fc9b411ef88f544c33c40cdbf5b04d57633799d13eaf2a690c501b026863539acb941880321c
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.2.0":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 9e57415bc69cd6efcc720b3b8fe9fdaf42dcfc06f86f0f45378b1fa512598a8aac48aa3928c8751d58e2f01bb4ba4f07e4f3d9bc0d57586d45f1bd1e872c6cde
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.0":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 222512e9315a0efca1276af9adb2127f02105d7288fa746145bf45e2716383fb79eb983c89601a72a399a56b7c18d38ce70457c5466218c5f13fad957cee16df
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:^2.0.6":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: dc1f063c2c6ac9533aee14d406441f86783a8984b2ca09b19c2fe281f9ff59d315298bc7bc22fd1f83d26fe19ef2f20e2ddb68e96b15040292e555c5ced0c1e4
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.15.0
  resolution: "fastq@npm:1.15.0"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 67c01b1c972e2d5b6fea197a1a39d5d582982aea69ff4c504badac71080d8396d4843b165a9686e907c233048f15a86bbccb0e7f83ba771f6fa24bcde059d0c3
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: e260f7592fd196b4421504d3597cc76f4a1ca7a9488260d533b611fc3cefd61e9a9be1417cb82d3b01ad9f9c0ff2dbf258e1026d2445e26b0cf5148ff4250429
  languageName: node
  linkType: hard

"finalhandler@npm:1.2.0":
  version: 1.2.0
  resolution: "finalhandler@npm:1.2.0"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 635718cb203c6d18e6b48dfbb6c54ccb08ea470e4f474ddcef38c47edcf3227feec316f886dd701235997d8af35240cae49856721ce18f539ad038665ebbf163
  languageName: node
  linkType: hard

"find-up@npm:5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"find-up@npm:^2.1.0":
  version: 2.1.0
  resolution: "find-up@npm:2.1.0"
  dependencies:
    locate-path: "npm:^2.0.0"
  checksum: 43284fe4da09f89011f08e3c32cd38401e786b19226ea440b75386c1b12a4cb738c94969808d53a84f564ede22f732c8409e3cfc3f7fb5b5c32378ad0bbf28bd
  languageName: node
  linkType: hard

"flat@npm:^5.0.2":
  version: 5.0.2
  resolution: "flat@npm:5.0.2"
  bin:
    flat: cli.js
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fmix@npm:^0.1.0":
  version: 0.1.0
  resolution: "fmix@npm:0.1.0"
  dependencies:
    imul: "npm:^1.0.0"
  checksum: c465344d4f169eaf10d45c33949a1e7a633f09dba2ac7063ce8ae8be743df5979d708f7f24900163589f047f5194ac5fc2476177ce31175e8805adfa7b8fb7a4
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.12.1, follow-redirects@npm:^1.14.0":
  version: 1.15.3
  resolution: "follow-redirects@npm:1.15.3"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 60d98693f4976892f8c654b16ef6d1803887a951898857ab0cdc009570b1c06314ad499505b7a040ac5b98144939f8597766e5e6a6859c0945d157b473aa6f5f
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: "npm:^1.1.3"
  checksum: fdac0cde1be35610bd635ae958422e8ce0cc1313e8d32ea6d34cfda7b60850940c1fd07c36456ad76bd9c24aef6ff5e03b02beb58c83af5ef6c968a64eada676
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.1.1
  resolution: "foreground-child@npm:3.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 087edd44857d258c4f73ad84cb8df980826569656f2550c341b27adf5335354393eec24ea2fabd43a253233fb27cee177ebe46bd0b7ea129c77e87cb1e9936fb
  languageName: node
  linkType: hard

"forever-agent@npm:~0.6.1":
  version: 0.6.1
  resolution: "forever-agent@npm:0.6.1"
  checksum: c1e1644d5e074ac063ecbc3fb8582013ef91fff0e3fa41e76db23d2f62bc6d9677aac86db950917deed4fe1fdd772df780cfaa352075f23deec9c015313afb97
  languageName: node
  linkType: hard

"form-data-encoder@npm:1.7.1":
  version: 1.7.1
  resolution: "form-data-encoder@npm:1.7.1"
  checksum: 1abc9059d991b105ba4122a36f9b5c17fd0af77ce8fa59a826a5b9ce56d616807e7780963616dd7e7906ec7aa1ba28cfb7c9defd9747ad10484e039a2b946cca
  languageName: node
  linkType: hard

"form-data-encoder@npm:^2.1.2":
  version: 2.1.4
  resolution: "form-data-encoder@npm:2.1.4"
  checksum: 3778e7db3c21457296e6fdbc4200642a6c01e8be9297256e845ee275f9ddaecb5f49bfb0364690ad216898c114ec59bf85f01ec823a70670b8067273415d62f6
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.0
  resolution: "form-data@npm:4.0.0"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    mime-types: "npm:^2.1.12"
  checksum: 7264aa760a8cf09482816d8300f1b6e2423de1b02bba612a136857413fdc96d7178298ced106817655facc6b89036c6e12ae31c9eb5bdc16aabf502ae8a5d805
  languageName: node
  linkType: hard

"form-data@npm:~2.3.2":
  version: 2.3.3
  resolution: "form-data@npm:2.3.3"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.6"
    mime-types: "npm:^2.1.12"
  checksum: 1b6f3ccbf4540e535887b42218a2431a3f6cfdea320119c2affa2a7a374ad8fdd1e60166fc865181f45d49b1684c3e90e7b2190d3fe016692957afb9cf0d0d02
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 29ba9fd347117144e97cbb8852baae5e8b2acb7d1b591ef85695ed96f5b933b1804a7fac4a15dd09ca7ac7d0cdc104410e8102aae2dd3faa570a797ba07adb81
  languageName: node
  linkType: hard

"fp-ts@npm:1.19.3":
  version: 1.19.3
  resolution: "fp-ts@npm:1.19.3"
  checksum: 3b3426f9a033b3e1b43f68da1baeb9d25b1a7cfeda0f55d4eadf0a1ab951898edc8b3453e4fec3113c140c98fdbf5fe8ab5232d349376ea7920e280af4e52050
  languageName: node
  linkType: hard

"fp-ts@npm:^1.0.0":
  version: 1.19.5
  resolution: "fp-ts@npm:1.19.5"
  checksum: 17aa04bbbba9096ac32efd4f192de6211687cab195c423d4072a904f1346c2d508243880685d6f4bb4be29e5f337a67cfa211645e491491683b6aaff23b5dd4a
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 64c88e489b5d08e2f29664eb3c79c705ff9a8eb15d3e597198ef76546d4ade295897a44abb0abd2700e7ef784b2e3cbf1161e4fbf16f59129193fd1030d16da1
  languageName: node
  linkType: hard

"fs-extra@npm:^0.30.0":
  version: 0.30.0
  resolution: "fs-extra@npm:0.30.0"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    jsonfile: "npm:^2.1.0"
    klaw: "npm:^1.0.0"
    path-is-absolute: "npm:^1.0.0"
    rimraf: "npm:^2.2.8"
  checksum: bfdd95f598a36a3f24b02db840c1dc54facba2793dea06355c75a6ed823f92e4033589e287f2b91a02a9980c3fb44099e3f00fce5230f045c87431f69be26084
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 05ce2c3b59049bcb7b52001acd000e44b3c4af4ec1f8839f383ef41ec0048e3cfa7fd8a637b1bddfefad319145db89be91f4b7c1db2908205d38bf91e7d1d3b7
  languageName: node
  linkType: hard

"fs-extra@npm:^4.0.2":
  version: 4.0.3
  resolution: "fs-extra@npm:4.0.3"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: c1ab28ac6b19a1e37f9c0fb3a233b7333bd4d12ea2a514b5469ba956f022fa0e2aefa3b351d1117b80ed45495bb779427c8f64727c150bb1599c2ce9ab3b42ac
  languageName: node
  linkType: hard

"fs-extra@npm:^7.0.1":
  version: 7.0.1
  resolution: "fs-extra@npm:7.0.1"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 3fc6e56ba2f07c00d452163f27f21a7076b72ef7da8a50fef004336d59ef4c34deda11d10ecd73fd8fbcf20e4f575f52857293090b3c9f8741d4e0598be30fea
  languageName: node
  linkType: hard

"fs-minipass@npm:^1.2.7":
  version: 1.2.7
  resolution: "fs-minipass@npm:1.2.7"
  dependencies:
    minipass: "npm:^2.6.0"
  checksum: 6a2d39963eaad748164530ffab49606d0f3462c7867748521af3b7039d13689be533636d50a04e8ba6bd327d4d2e899d0907f8830d1161fe2db467d59cc46dc3
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 03191781e94bc9a54bd376d3146f90fe8e082627c502185dbf7b9b3032f66b0b142c1115f3b2cc5936575fc1b44845ce903dd4c21bec2a8d69f3bd56f9cee9ec
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: e703107c28e362d8d7b910bbcbfd371e640a3bb45ae157a362b5952c0030c0b6d4981140ec319b347bce7adc025dd7813da1ff908a945ac214d64f5402a51b96
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"functional-red-black-tree@npm:^1.0.1":
  version: 1.0.1
  resolution: "functional-red-black-tree@npm:1.0.1"
  checksum: debe73e92204341d1fa5f89614e44284d3add26dee660722978d8c50829170f87d1c74768f68c251d215ae461c11db7bac13101c77f4146ff051da75466f7a12
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-func-name@npm:^2.0.1, get-func-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "get-func-name@npm:2.0.2"
  checksum: 3f62f4c23647de9d46e6f76d2b3eafe58933a9b3830c60669e4180d6c601ce1b4aa310ba8366143f55e52b139f992087a9f0647274e8745621fa2af7e0acf13b
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.1, get-intrinsic@npm:^1.2.2":
  version: 1.2.2
  resolution: "get-intrinsic@npm:1.2.2"
  dependencies:
    function-bind: "npm:^1.1.2"
    has-proto: "npm:^1.0.1"
    has-symbols: "npm:^1.0.3"
    hasown: "npm:^2.0.0"
  checksum: aa96db4f809734d26d49b59bc8669d73a0ae792da561514e987735573a1dfaede516cd102f217a078ea2b42d4c4fb1f83d487932cb15d49826b726cc9cd4470b
  languageName: node
  linkType: hard

"get-stdin@npm:^9.0.0":
  version: 9.0.0
  resolution: "get-stdin@npm:9.0.0"
  checksum: 5972bc34d05932b45512c8e2d67b040f1c1ca8afb95c56cbc480985f2d761b7e37fe90dc8abd22527f062cc5639a6930ff346e9952ae4c11a2d4275869459594
  languageName: node
  linkType: hard

"get-stream@npm:^5.1.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: "npm:^3.0.0"
  checksum: 13a73148dca795e41421013da6e3ebff8ccb7fba4d2f023fd0c6da2c166ec4e789bec9774a73a7b49c08daf2cae552f8a3e914042ac23b5f59dd278cc8f9cbfb
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0, get-stream@npm:^6.0.1":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 781266d29725f35c59f1d214aedc92b0ae855800a980800e2923b3fbc4e56b3cb6e462c42e09a1cf1a00c64e056a78fa407cbe06c7c92b7e5cd49b4b85c2a497
  languageName: node
  linkType: hard

"getpass@npm:^0.1.1":
  version: 0.1.7
  resolution: "getpass@npm:0.1.7"
  dependencies:
    assert-plus: "npm:^1.0.0"
  checksum: ab18d55661db264e3eac6012c2d3daeafaab7a501c035ae0ccb193c3c23e9849c6e29b6ac762b9c2adae460266f925d55a3a2a3a3c8b94be2f222df94d70c046
  languageName: node
  linkType: hard

"git-hooks-list@npm:^3.0.0":
  version: 3.1.0
  resolution: "git-hooks-list@npm:3.1.0"
  checksum: 05cbdb29e1e14f3b6fde78c876a34383e4476b1be32e8486ad03293f01add884c1a8df8c2dce2ca5d99119c94951b2ff9fa9cbd51d834ae6477b6813cefb998f
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob@npm:7.2.0":
  version: 7.2.0
  resolution: "glob@npm:7.2.0"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.0.4"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: bc78b6ea0735b6e23d20678aba4ae6a4760e8c9527e3c4683ac25b14e70f55f9531245dcf25959b70cbc4aa3dcce1fc37ab65fd026a4cbd70aa3a44880bd396b
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.3.10
  resolution: "glob@npm:10.3.10"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^2.3.5"
    minimatch: "npm:^9.0.1"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
    path-scurry: "npm:^1.10.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 38bdb2c9ce75eb5ed168f309d4ed05b0798f640b637034800a6bf306f39d35409bf278b0eaaffaec07591085d3acb7184a201eae791468f0f617771c2486a6a8
  languageName: node
  linkType: hard

"glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 59452a9202c81d4508a43b8af7082ca5c76452b9fcc4a9ab17655822e6ce9b21d4f8fbadabe4fe3faef448294cec249af305e2cd824b7e9aaf689240e5e96a7b
  languageName: node
  linkType: hard

"glob@npm:^8.0.3":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^5.0.1"
    once: "npm:^1.3.0"
  checksum: 9aab1c75eb087c35dbc41d1f742e51d0507aa2b14c910d96fb8287107a10a22f4bbdce26fc0a3da4c69a20f7b26d62f1640b346a4f6e6becfff47f335bb1dc5e
  languageName: node
  linkType: hard

"global@npm:~4.4.0":
  version: 4.4.0
  resolution: "global@npm:4.4.0"
  dependencies:
    min-document: "npm:^2.19.0"
    process: "npm:^0.11.10"
  checksum: 9c057557c8f5a5bcfbeb9378ba4fe2255d04679452be504608dd5f13b54edf79f7be1db1031ea06a4ec6edd3b9f5f17d2d172fb47e6c69dae57fd84b7e72b77f
  languageName: node
  linkType: hard

"globby@npm:^13.1.2":
  version: 13.2.2
  resolution: "globby@npm:13.2.2"
  dependencies:
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.3.0"
    ignore: "npm:^5.2.4"
    merge2: "npm:^1.4.1"
    slash: "npm:^4.0.0"
  checksum: 4494a9d2162a7e4d327988b26be66d8eab87d7f59a83219e74b065e2c3ced23698f68fb10482bf9337133819281803fb886d6ae06afbb2affa743623eb0b1949
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.1.3"
  checksum: 5fbc7ad57b368ae4cd2f41214bd947b045c1a4be2f194a7be1778d71f8af9dbf4004221f3b6f23e30820eb0d052b4f819fe6ebe8221e2a3c6f0ee4ef173421ca
  languageName: node
  linkType: hard

"got@npm:12.1.0":
  version: 12.1.0
  resolution: "got@npm:12.1.0"
  dependencies:
    "@sindresorhus/is": "npm:^4.6.0"
    "@szmarczak/http-timer": "npm:^5.0.1"
    "@types/cacheable-request": "npm:^6.0.2"
    "@types/responselike": "npm:^1.0.0"
    cacheable-lookup: "npm:^6.0.4"
    cacheable-request: "npm:^7.0.2"
    decompress-response: "npm:^6.0.0"
    form-data-encoder: "npm:1.7.1"
    get-stream: "npm:^6.0.1"
    http2-wrapper: "npm:^2.1.10"
    lowercase-keys: "npm:^3.0.0"
    p-cancelable: "npm:^3.0.0"
    responselike: "npm:^2.0.0"
  checksum: d1dab1884b14d1f59d10005ee3834faf6d9b43530c7faf603c176d35dceb2b8e0e2e01b9e0d4fc320409ac1b4d958196ff928dc6df0ddd0a3e7a254aa9edfd45
  languageName: node
  linkType: hard

"got@npm:^11.8.5":
  version: 11.8.6
  resolution: "got@npm:11.8.6"
  dependencies:
    "@sindresorhus/is": "npm:^4.0.0"
    "@szmarczak/http-timer": "npm:^4.0.5"
    "@types/cacheable-request": "npm:^6.0.1"
    "@types/responselike": "npm:^1.0.0"
    cacheable-lookup: "npm:^5.0.3"
    cacheable-request: "npm:^7.0.2"
    decompress-response: "npm:^6.0.0"
    http2-wrapper: "npm:^1.0.0-beta.5.2"
    lowercase-keys: "npm:^2.0.0"
    p-cancelable: "npm:^2.0.0"
    responselike: "npm:^2.0.0"
  checksum: a30c74029d81bd5fe50dea1a0c970595d792c568e188ff8be254b5bc11e6158d1b014570772d4a30d0a97723e7dd34e7c8cc1a2f23018f60aece3070a7a5c2a5
  languageName: node
  linkType: hard

"got@npm:^12.1.0":
  version: 12.6.1
  resolution: "got@npm:12.6.1"
  dependencies:
    "@sindresorhus/is": "npm:^5.2.0"
    "@szmarczak/http-timer": "npm:^5.0.1"
    cacheable-lookup: "npm:^7.0.0"
    cacheable-request: "npm:^10.2.8"
    decompress-response: "npm:^6.0.0"
    form-data-encoder: "npm:^2.1.2"
    get-stream: "npm:^6.0.1"
    http2-wrapper: "npm:^2.1.10"
    lowercase-keys: "npm:^3.0.0"
    p-cancelable: "npm:^3.0.0"
    responselike: "npm:^3.0.0"
  checksum: 6c22f1449f4574d79a38e0eba0b753ce2f9030d61838a1ae1e25d3ff5b0db7916aa21023ac369c67d39d17f87bba9283a0b0cb88590de77926c968630aacae75
  languageName: node
  linkType: hard

"graceful-fs@npm:4.2.10":
  version: 4.2.10
  resolution: "graceful-fs@npm:4.2.10"
  checksum: 0c83c52b62c68a944dcfb9d66b0f9f10f7d6e3d081e8067b9bfdc9e5f3a8896584d576036f82915773189eec1eba599397fc620e75c03c0610fb3d67c6713c1a
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.1.9, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"har-schema@npm:^2.0.0":
  version: 2.0.0
  resolution: "har-schema@npm:2.0.0"
  checksum: d8946348f333fb09e2bf24cc4c67eabb47c8e1d1aa1c14184c7ffec1140a49ec8aa78aa93677ae452d71d5fc0fdeec20f0c8c1237291fc2bcb3f502a5d204f9b
  languageName: node
  linkType: hard

"har-validator@npm:~5.1.3":
  version: 5.1.5
  resolution: "har-validator@npm:5.1.5"
  dependencies:
    ajv: "npm:^6.12.3"
    har-schema: "npm:^2.0.0"
  checksum: b998a7269ca560d7f219eedc53e2c664cd87d487e428ae854a6af4573fc94f182fe9d2e3b92ab968249baec7ebaf9ead69cf975c931dc2ab282ec182ee988280
  languageName: node
  linkType: hard

"hardhat-deploy@npm:0.11.27":
  version: 0.11.27
  resolution: "hardhat-deploy@npm:0.11.27"
  dependencies:
    "@ethersproject/abi": "npm:^5.7.0"
    "@ethersproject/abstract-signer": "npm:^5.7.0"
    "@ethersproject/address": "npm:^5.7.0"
    "@ethersproject/bignumber": "npm:^5.7.0"
    "@ethersproject/bytes": "npm:^5.7.0"
    "@ethersproject/constants": "npm:^5.7.0"
    "@ethersproject/contracts": "npm:^5.7.0"
    "@ethersproject/providers": "npm:^5.7.2"
    "@ethersproject/solidity": "npm:^5.7.0"
    "@ethersproject/transactions": "npm:^5.7.0"
    "@ethersproject/wallet": "npm:^5.7.0"
    "@types/qs": "npm:^6.9.7"
    axios: "npm:^0.21.1"
    chalk: "npm:^4.1.2"
    chokidar: "npm:^3.5.2"
    debug: "npm:^4.3.2"
    enquirer: "npm:^2.3.6"
    ethers: "npm:^5.5.3"
    form-data: "npm:^4.0.0"
    fs-extra: "npm:^10.0.0"
    match-all: "npm:^1.2.6"
    murmur-128: "npm:^0.2.1"
    qs: "npm:^6.9.4"
    zksync-web3: "npm:^0.14.3"
  checksum: 49f404ad28aa12d1a2260667443dd402dcc6f83e40a00750de298b7285bf3bf61aed4e0f8a78ceabff92171ef0b4e1c3f67db8358560255cbdcb74cc47037405
  languageName: node
  linkType: hard

"hardhat@npm:^2.9.0":
  version: 2.19.2
  resolution: "hardhat@npm:2.19.2"
  dependencies:
    "@ethersproject/abi": "npm:^5.1.2"
    "@metamask/eth-sig-util": "npm:^4.0.0"
    "@nomicfoundation/ethereumjs-block": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-blockchain": "npm:7.0.2"
    "@nomicfoundation/ethereumjs-common": "npm:4.0.2"
    "@nomicfoundation/ethereumjs-evm": "npm:2.0.2"
    "@nomicfoundation/ethereumjs-rlp": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-statemanager": "npm:2.0.2"
    "@nomicfoundation/ethereumjs-trie": "npm:6.0.2"
    "@nomicfoundation/ethereumjs-tx": "npm:5.0.2"
    "@nomicfoundation/ethereumjs-util": "npm:9.0.2"
    "@nomicfoundation/ethereumjs-vm": "npm:7.0.2"
    "@nomicfoundation/solidity-analyzer": "npm:^0.1.0"
    "@sentry/node": "npm:^5.18.1"
    "@types/bn.js": "npm:^5.1.0"
    "@types/lru-cache": "npm:^5.1.0"
    adm-zip: "npm:^0.4.16"
    aggregate-error: "npm:^3.0.0"
    ansi-escapes: "npm:^4.3.0"
    chalk: "npm:^2.4.2"
    chokidar: "npm:^3.4.0"
    ci-info: "npm:^2.0.0"
    debug: "npm:^4.1.1"
    enquirer: "npm:^2.3.0"
    env-paths: "npm:^2.2.0"
    ethereum-cryptography: "npm:^1.0.3"
    ethereumjs-abi: "npm:^0.6.8"
    find-up: "npm:^2.1.0"
    fp-ts: "npm:1.19.3"
    fs-extra: "npm:^7.0.1"
    glob: "npm:7.2.0"
    immutable: "npm:^4.0.0-rc.12"
    io-ts: "npm:1.10.4"
    keccak: "npm:^3.0.2"
    lodash: "npm:^4.17.11"
    mnemonist: "npm:^0.38.0"
    mocha: "npm:^10.0.0"
    p-map: "npm:^4.0.0"
    raw-body: "npm:^2.4.1"
    resolve: "npm:1.17.0"
    semver: "npm:^6.3.0"
    solc: "npm:0.7.3"
    source-map-support: "npm:^0.5.13"
    stacktrace-parser: "npm:^0.1.10"
    tsort: "npm:0.0.1"
    undici: "npm:^5.14.0"
    uuid: "npm:^8.3.2"
    ws: "npm:^7.4.6"
  peerDependencies:
    ts-node: "*"
    typescript: "*"
  peerDependenciesMeta:
    ts-node:
      optional: true
    typescript:
      optional: true
  bin:
    hardhat: internal/cli/bootstrap.js
  checksum: 74913ce606e5e7ebe4b798767d68bd11b838738a35fab578d555dafaa9d4033e401ad2287248720add83503a8ed671b2b34f88560154299911739412711561f1
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0":
  version: 1.0.1
  resolution: "has-property-descriptors@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.2"
  checksum: 21a47bb080a24e79594aef1ce71e1a18a1c5ab4120308e218088f67ebb7f6f408847541e2d96e5bd00e90eef5c5a49e4ebbdc8fc2d5b365a2c379aef071642f0
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-proto@npm:1.0.1"
  checksum: eab2ab0ed1eae6d058b9bbc4c1d99d2751b29717be80d02fd03ead8b62675488de0c7359bc1fdd4b87ef6fd11e796a9631ad4d7452d9324fdada70158c2e5be7
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.2, has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: 464f97a8202a7690dadd026e6d73b1ceeddd60fe6acfd06151106f050303eaa75855aaa94969df8015c11ff7c505f196114d22f7386b4a471038da5874cf5e9b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-tostringtag@npm:1.0.0"
  dependencies:
    has-symbols: "npm:^1.0.2"
  checksum: 95546e7132efc895a9ae64a8a7cf52588601fc3d52e0304ed228f336992cdf0baaba6f3519d2655e560467db35a1ed79f6420c286cc91a13aa0647a31ed92570
  languageName: node
  linkType: hard

"hash-base@npm:^3.0.0":
  version: 3.1.0
  resolution: "hash-base@npm:3.1.0"
  dependencies:
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.6.0"
    safe-buffer: "npm:^5.2.0"
  checksum: 26b7e97ac3de13cb23fc3145e7e3450b0530274a9562144fc2bf5c1e2983afd0e09ed7cc3b20974ba66039fad316db463da80eb452e7373e780cbee9a0d2f2dc
  languageName: node
  linkType: hard

"hash.js@npm:1.1.7, hash.js@npm:^1.0.0, hash.js@npm:^1.0.3, hash.js@npm:^1.1.7":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: "npm:^2.0.3"
    minimalistic-assert: "npm:^1.0.1"
  checksum: 0c89ee4006606a40f92df5cc3c263342e7fea68110f3e9ef032bd2083650430505db01b6b7926953489517d4027535e4fdc7f970412893d3031c361d3ec8f4b3
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0":
  version: 2.0.0
  resolution: "hasown@npm:2.0.0"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: c330f8d93f9d23fe632c719d4db3d698ef7d7c367d51548b836069e06a90fa9151e868c8e67353cfe98d67865bf7354855db28fa36eb1b18fa5d4a3f4e7f1c90
  languageName: node
  linkType: hard

"he@npm:1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: d09b2243da4e23f53336e8de3093e5c43d2c39f8d0d18817abfa32ce3e9355391b2edb4bb5edc376aea5d4b0b59d6a0482aab4c52bc02ef95751e4b818e847f1
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: "npm:^1.0.3"
    minimalistic-assert: "npm:^1.0.0"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 0298a1445b8029a69b713d918ecaa84a1d9f614f5857e0c6e1ca517abfa1357216987b2ee08cc6cc73ba82a6c6ddf2ff11b9717a653530ef03be599d4699b836
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.0.0, http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 362d5ed66b12ceb9c0a328fb31200b590ab1b02f4a254a697dc796850cc4385603e75f53ec59f768b2dad3bfa1464bd229f7de278d2899a0e3beffc634b6683f
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 0e7f76ee8ff8a33e58a3281a469815b893c41357378f408be8f6d4aa7d1efafb0da064625518e7078381b6a92325949b119dc38fcb30bdbc4e3a35f78c44c439
  languageName: node
  linkType: hard

"http-https@npm:^1.0.0":
  version: 1.0.0
  resolution: "http-https@npm:1.0.0"
  checksum: fd3c0802982b1e951a03206690271dacb641b39b80d1820e95095db923d8f63cc7f0df1259969400c8487787a2a46f7b33383c0427ec780a78131b153741b144
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "http-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: dbaaf3d9f3fc4df4a5d7ec45d456ec50f575240b557160fa63427b447d1f812dd7fe4a4f17d2e1ba003d231f07edf5a856ea6d91cb32d533062ff20a7803ccac
  languageName: node
  linkType: hard

"http-signature@npm:~1.2.0":
  version: 1.2.0
  resolution: "http-signature@npm:1.2.0"
  dependencies:
    assert-plus: "npm:^1.0.0"
    jsprim: "npm:^1.2.2"
    sshpk: "npm:^1.7.0"
  checksum: 2ff7112e6b0d8f08b382dfe705078c655501f2ddd76cf589d108445a9dd388a0a9be928c37108261519a7f53e6bbd1651048d74057b804807cce1ec49e87a95b
  languageName: node
  linkType: hard

"http2-wrapper@npm:^1.0.0-beta.5.2":
  version: 1.0.3
  resolution: "http2-wrapper@npm:1.0.3"
  dependencies:
    quick-lru: "npm:^5.1.1"
    resolve-alpn: "npm:^1.0.0"
  checksum: 8097ee2699440c2e64bda52124990cc5b0fb347401c7797b1a0c1efd5a0f79a4ebaa68e8a6ac3e2dde5f09460c1602764da6da2412bad628ed0a3b0ae35e72d4
  languageName: node
  linkType: hard

"http2-wrapper@npm:^2.1.10":
  version: 2.2.1
  resolution: "http2-wrapper@npm:2.2.1"
  dependencies:
    quick-lru: "npm:^5.1.1"
    resolve-alpn: "npm:^1.2.0"
  checksum: e7a5ac6548318e83fc0399cd832cdff6bbf902b165d211cad47a56ee732922e0aa1107246dd884b12532a1c4649d27c4d44f2480911c65202e93c90bde8fa29d
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: f0dce7bdcac5e8eaa0be3c7368bb8836ed010fb5b6349ffb412b172a203efe8f807d9a6681319105ea1b6901e1972c7b5ea899672a7b9aad58309f766dcbe0df
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.2
  resolution: "https-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.0.2"
    debug: "npm:4"
  checksum: 9ec844f78fd643608239c9c3f6819918631df5cd3e17d104cc507226a39b5d4adda9d790fc9fd63ac0d2bb8a761b2f9f60faa80584a9bf9d7f2e8c5ed0acd330
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: df59be9e0af479036798a881d1f136c4a29e0b518d4abb863afbd11bf30efa3eeb1d0425fc65942dcc05ab3bf40205ea436b0ff389f2cd20b75b8643d539bf86
  languageName: node
  linkType: hard

"human-signals@npm:^4.3.0":
  version: 4.3.1
  resolution: "human-signals@npm:4.3.1"
  checksum: fa59894c358fe9f2b5549be2fb083661d5e1dff618d3ac70a49ca73495a72e873fbf6c0878561478e521e17d498292746ee391791db95ffe5747bfb5aef8765b
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 6d3a2dac6e5d1fb126d25645c25c3a1209f70cceecc68b8ef51ae0da3cdc078c151fade7524a30b12a3094926336831fca09c666ef55b37e2c69638b5d6bd2e3
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"idna-uts46-hx@npm:^2.3.1":
  version: 2.3.1
  resolution: "idna-uts46-hx@npm:2.3.1"
  dependencies:
    punycode: "npm:2.1.0"
  checksum: 5cb65dbc375d42ce9b38dab6e2a7f41b8c059f9a88d236bc9ca32084485f5f22fec11ea5b4e6b61239448148443c3f825fddaa5f298d22e12ecfe845de71a807
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13, ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: d9f2557a59036f16c282aaeb107832dc957a93d73397d89bbad4eb1130560560eb695060145e8e6b3b498b15ab95510226649a0b8f52ae06583575419fe10fc4
  languageName: node
  linkType: hard

"ignore@npm:^5.2.4":
  version: 5.3.0
  resolution: "ignore@npm:5.3.0"
  checksum: 51594355cea4c6ad6b28b3b85eb81afa7b988a1871feefd7062baf136c95aa06760ee934fa9590e43d967bd377ce84a4cf6135fbeb6063e063f1182a0e9a3bcd
  languageName: node
  linkType: hard

"immediate@npm:^3.2.3":
  version: 3.3.0
  resolution: "immediate@npm:3.3.0"
  checksum: 39aefd16e7d423a0435f12ed47e45cc18fbb5825fea56d573805f68a056ab5727a16ea79893d35db565f9de14a224bfabffa5e5e2c422117c5fa24428ac0aa69
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0-rc.12":
  version: 4.3.4
  resolution: "immutable@npm:4.3.4"
  checksum: ea187acc1eec9dcfaa0823bae59e1ae0ea82e7a40d2ace9fb84d467875d5506ced684a79b68e70451f1e1761a387a958ba724171f93aa10330998b026fcb5d29
  languageName: node
  linkType: hard

"import-fresh@npm:^3.3.0":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"imul@npm:^1.0.0":
  version: 1.0.1
  resolution: "imul@npm:1.0.1"
  checksum: 6c2af3d5f09e2135e14d565a2c108412b825b221eb2c881f9130467f2adccf7ae201773ae8bcf1be169e2d090567a1fdfa9cf20d3b7da7b9cecb95b920ff3e52
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: cd3f5cbc9ca2d624c6a1f53f12e6b341659aba0e2d3254ae2b4464aaea8b4294cdb09616abbc59458f980531f2429784ed6a420d48d245bcad0811980c9efae9
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: d2ebd65441a38c8336c223d1b80b921b9fa737e37ea466fd7e253cb000c64ae1f17fa59e68130ef5bda92cfd8d36b83d37dab0eb0a4558bcfec8e8cdfd2dcb67
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.1, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: cd45e923bee15186c07fa4c89db0aace24824c482fb887b528304694b2aa6ff8a898da8657046a5dcf3e46cd6db6c61629551f9215f208d7c3f157cf9b290521
  languageName: node
  linkType: hard

"ini@npm:^1.3.4, ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 314ae176e8d4deb3def56106da8002b462221c174ddb7ce0c49ee72c8cd1f9044f7b10cc555a7d8850982c3b9ca96fc212122749f5234bc2b6fb05fb942ed566
  languageName: node
  linkType: hard

"io-ts@npm:1.10.4":
  version: 1.10.4
  resolution: "io-ts@npm:1.10.4"
  dependencies:
    fp-ts: "npm:^1.0.0"
  checksum: d68cb0928b37485cf631c923628dd189784d3dbbcb2d681d86f5c64b9b0321aa33bd2ff271381ac54a279aec5935ff7a743264c858b5172e83b6a9f0cbafc7d1
  languageName: node
  linkType: hard

"ip@npm:^2.0.0":
  version: 2.0.0
  resolution: "ip@npm:2.0.0"
  checksum: 1270b11e534a466fb4cf4426cbcc3a907c429389f7f4e4e3b288b42823562e88d6a509ceda8141a507de147ca506141f745005c0aa144569d94cf24a54eb52bc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 864d0cced0c0832700e9621913a6429ccdc67f37c1bd78fb8c6789fff35c9d167cb329134acad2290497a53336813ab4798d2794fd675d5eb33b5fdf0982b9ca
  languageName: node
  linkType: hard

"is-arguments@npm:^1.0.4":
  version: 1.1.1
  resolution: "is-arguments@npm:1.1.1"
  dependencies:
    call-bind: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.0"
  checksum: a170c7e26082e10de9be6e96d32ae3db4d5906194051b792e85fae3393b53cf2cb5b3557863e5c8ccbab55e2fd8f2f75aa643d437613f72052cf0356615c34be
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 73ced84fa35e59e2c57da2d01e12cd01479f381d7f122ce41dcbb713f09dbfc651315832cd2bf8accba7681a69e4d6f1e03941d94dd10040d415086360e7005e
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 078e51b4f956c2c5fd2b26bb2672c3ccf7e1faff38e0ebdba45612265f4e3d9fc3127a1fa8370bbf09eab61339203c3d3b7af5662cbf8be4030f8fac37745b0e
  languageName: node
  linkType: hard

"is-buffer@npm:^2.0.5":
  version: 2.0.5
  resolution: "is-buffer@npm:2.0.5"
  checksum: 3261a8b858edcc6c9566ba1694bf829e126faa88911d1c0a747ea658c5d81b14b6955e3a702d59dabadd58fdd440c01f321aa71d6547105fd21d03f94d0597e7
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 48a9297fb92c99e9df48706241a189da362bff3003354aea4048bd5f7b2eb0d823cd16d0a383cece3d76166ba16d85d9659165ac6fcce1ac12e6c649d66dbdb9
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0":
  version: 2.13.1
  resolution: "is-core-module@npm:2.13.1"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: d53bd0cc24b0a0351fb4b206ee3908f71b9bbf1c47e9c9e14e5f06d292af1663704d2abd7e67700d6487b2b7864e0d0f6f10a1edf1892864bdffcb197d1845a2
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: b698118f04feb7eaf3338922bd79cba064ea54a1c3db6ec8c0c8d8ee7613e7e5854d802d3ef646812a8a3ace81182a085dfa0a71cc68b06f3fa794b9783b3c90
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fn@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-fn@npm:1.0.0"
  checksum: eeea1e536716f93a92dc1a8550b2c0909fe74bb5144d0fb6d65e0d31eb9c06c30559f69d83a9351d2288cc7293b43bc074e0fab5fae19e312ff38aa0c7672827
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-function@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-function@npm:1.0.2"
  checksum: 7d564562e07b4b51359547d3ccc10fb93bb392fd1b8177ae2601ee4982a0ece86d952323fc172a9000743a3971f09689495ab78a1d49a9b14fc97a7e28521dc0
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.7":
  version: 1.0.10
  resolution: "is-generator-function@npm:1.0.10"
  dependencies:
    has-tostringtag: "npm:^1.0.0"
  checksum: 499a3ce6361064c3bd27fbff5c8000212d48506ebe1977842bbd7b3e708832d0deb1f4cc69186ece3640770e8c4f1287b24d99588a0b8058b2dbdd344bc1f47f
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-hex-prefixed@npm:1.0.0":
  version: 1.0.0
  resolution: "is-hex-prefixed@npm:1.0.0"
  checksum: 5ac58e6e528fb029cc43140f6eeb380fad23d0041cc23154b87f7c9a1b728bcf05909974e47248fd0b7fcc11ba33cf7e58d64804883056fabd23e2b898be41de
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: "npm:^3.0.0"
  bin:
    is-inside-container: cli.js
  checksum: c50b75a2ab66ab3e8b92b3bc534e1ea72ca25766832c0623ac22d134116a98bcf012197d1caabe1d1c4bd5f84363d4aa5c36bb4b585fbcaf57be172cd10a1a03
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-plain-obj@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: cec9100678b0a9fe0248a81743041ed990c2d4c99f893d935545cfbc42876cbe86d207f3b895700c690ad2fa520e568c44afc1605044b535a7820c1d40e38daa
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.1.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 6dc45da70d04a81f35c9310971e78a6a3c7a63547ef782e3a07ee3674695081b6ca4e977fbb8efc48dae3375e0b34558d2bcd722aec9bddfa2d7db5b041be8ce
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-stream@npm:3.0.0"
  checksum: 172093fe99119ffd07611ab6d1bcccfe8bc4aa80d864b15f43e63e54b7abc71e779acd69afdb854c4e2a67fdc16ae710e370eda40088d1cfc956a50ed82d8f16
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.3":
  version: 1.1.12
  resolution: "is-typed-array@npm:1.1.12"
  dependencies:
    which-typed-array: "npm:^1.1.11"
  checksum: d953adfd3c41618d5e01b2a10f21817e4cdc9572772fa17211100aebb3811b6e3c2e308a0558cc87d218a30504cb90154b833013437776551bfb70606fb088ca
  languageName: node
  linkType: hard

"is-typedarray@npm:^1.0.0, is-typedarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 4b433bfb0f9026f079f4eb3fbaa4ed2de17c9995c3a0b5c800bec40799b4b2a8b4e051b1ada77749deb9ded4ae52fe2096973f3a93ff83df1a5a7184a669478c
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:0.0.1":
  version: 0.0.1
  resolution: "isarray@npm:0.0.1"
  checksum: 49191f1425681df4a18c2f0f93db3adb85573bcdd6a4482539d98eac9e705d8961317b01175627e860516a2fc45f8f9302db26e5a380a97a520e272e2a40a8d4
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 1d8bc7911e13bb9f105b1b3e0b396c787a9e63046af0b8fe0ab1414488ab06b2b099b87a2d8a9e31d21c9a6fad773c7fc8b257c4880f2d957274479d28ca3414
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isstream@npm:~0.1.2":
  version: 0.1.2
  resolution: "isstream@npm:0.1.2"
  checksum: 22d9c181015226d4534a227539256897bbbcb7edd1066ca4fc4d3a06dbd976325dfdd16b3983c7d236a89f256805c1a685a772e0364e98873d3819b064ad35a1
  languageName: node
  linkType: hard

"jackspeak@npm:^2.3.5":
  version: 2.3.6
  resolution: "jackspeak@npm:2.3.6"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 6e6490d676af8c94a7b5b29b8fd5629f21346911ebe2e32931c2a54210134408171c24cee1a109df2ec19894ad04a429402a8438cbf5cc2794585d35428ace76
  languageName: node
  linkType: hard

"js-sdsl@npm:^4.1.4":
  version: 4.4.2
  resolution: "js-sdsl@npm:4.4.2"
  checksum: 806ab7aea38c15c323c6993b65abfad559d35de7d41ad1e2bf21498f1d4961ef863ac14cecbe667be3ada565dafe7a701096a6f49a275c5190eb1a1732430226
  languageName: node
  linkType: hard

"js-sha3@npm:0.8.0, js-sha3@npm:^0.8.0":
  version: 0.8.0
  resolution: "js-sha3@npm:0.8.0"
  checksum: a49ac6d3a6bfd7091472a28ab82a94c7fb8544cc584ee1906486536ba1cb4073a166f8c7bb2b0565eade23c5b3a7b8f7816231e0309ab5c549b737632377a20c
  languageName: node
  linkType: hard

"js-sha3@npm:^0.5.7":
  version: 0.5.7
  resolution: "js-sha3@npm:0.5.7"
  checksum: 32885c7edb50fca04017bacada8e5315c072d21d3d35e071e9640fc5577e200076a4718e0b2f33d86ab704accb68d2ade44f1e2ca424cc73a5929b9129dab948
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"js-yaml@npm:4.1.0, js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"jsbn@npm:~0.1.0":
  version: 0.1.1
  resolution: "jsbn@npm:0.1.1"
  checksum: 5450133242845100e694f0ef9175f44c012691a9b770b2571e677314e6f70600abb10777cdfc9a0c6a9f2ac6d134577403633de73e2fcd0f97875a67744e2d14
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 5f3a99009ed5f2a5a67d06e2f298cc97bc86d462034173308156f15b43a6e850be8511dc204b9b94566305da2947f7d90289657237d210351a39059ff9d666cf
  languageName: node
  linkType: hard

"json-rpc-engine@npm:^5.3.0":
  version: 5.4.0
  resolution: "json-rpc-engine@npm:5.4.0"
  dependencies:
    eth-rpc-errors: "npm:^3.0.0"
    safe-event-emitter: "npm:^1.0.1"
  checksum: 310af9dc256a14e3695f917912046afcab1fe716d6243616702bc2ebcbc7d164e3c2c04a5ff267e3930ef451e4cd8905651b656988bceb96a7034bf144eb8e67
  languageName: node
  linkType: hard

"json-rpc-engine@npm:^6.1.0":
  version: 6.1.0
  resolution: "json-rpc-engine@npm:6.1.0"
  dependencies:
    "@metamask/safe-event-emitter": "npm:^2.0.0"
    eth-rpc-errors: "npm:^4.0.2"
  checksum: 00d5b5228e90f126dd52176598db6e5611d295d3a3f7be21254c30c1b6555811260ef2ec2df035cd8e583e4b12096259da721e29f4ea2affb615f7dfc960a6a6
  languageName: node
  linkType: hard

"json-rpc-random-id@npm:^1.0.0, json-rpc-random-id@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-rpc-random-id@npm:1.0.1"
  checksum: fcd2e884193a129ace4002bd65a86e9cdb206733b4693baea77bd8b372cf8de3043fbea27716a2c9a716581a908ca8d978d9dfec4847eb2cf77edb4cf4b2252c
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-schema@npm:0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: 8b3b64eff4a807dc2a3045b104ed1b9335cd8d57aa74c58718f07f0f48b8baa3293b00af4dcfbdc9144c3aafea1e97982cc27cc8e150fc5d93c540649507a458
  languageName: node
  linkType: hard

"json-stable-stringify@npm:^1.0.1":
  version: 1.1.0
  resolution: "json-stable-stringify@npm:1.1.0"
  dependencies:
    call-bind: "npm:^1.0.5"
    isarray: "npm:^2.0.5"
    jsonify: "npm:^0.0.1"
    object-keys: "npm:^1.1.1"
  checksum: 2889eca4f39574905bde288791d3fcc79fc9952f445a5fefb82af175a7992ec48c64161421c1e142f553a14a5f541de2e173cb22ce61d7fffc36d4bb44720541
  languageName: node
  linkType: hard

"json-stringify-safe@npm:~5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 59169a081e4eeb6f9559ae1f938f656191c000e0512aa6df9f3c8b2437a4ab1823819c6b9fd1818a4e39593ccfd72e9a051fdd3e2d1e340ed913679e888ded8c
  languageName: node
  linkType: hard

"jsonfile@npm:^2.1.0":
  version: 2.4.0
  resolution: "jsonfile@npm:2.4.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 517656e0a7c4eda5a90341dd0ec9e9b7590d0c77d66d8aad0162615dfc7c5f219c82565b927cc4cc774ca93e484d118a274ef0def74279a3d8afb4ff2f4e4800
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 17796f0ab1be8479827d3683433f97ebe0a1c6932c3360fa40348eac36904d69269aab26f8b16da311882d94b42e9208e8b28e490bf926364f3ac9bff134c226
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 03014769e7dc77d4cf05fa0b534907270b60890085dd5e4d60a382ff09328580651da0b8b4cdf44d91e4c8ae64d91791d965f05707beff000ed494a38b6fec85
  languageName: node
  linkType: hard

"jsonify@npm:^0.0.1":
  version: 0.0.1
  resolution: "jsonify@npm:0.0.1"
  checksum: 7b86b6f4518582ff1d8b7624ed6c6277affd5246445e864615dbdef843a4057ac58587684faf129ea111eeb80e01c15f0a4d9d03820eb3f3985fa67e81b12398
  languageName: node
  linkType: hard

"jsprim@npm:^1.2.2":
  version: 1.4.2
  resolution: "jsprim@npm:1.4.2"
  dependencies:
    assert-plus: "npm:1.0.0"
    extsprintf: "npm:1.3.0"
    json-schema: "npm:0.4.0"
    verror: "npm:1.10.0"
  checksum: df2bf234eab1b5078d01bcbff3553d50a243f7b5c10a169745efeda6344d62798bd1d85bcca6a8446f3b5d0495e989db45f9de8dae219f0f9796e70e0c776089
  languageName: node
  linkType: hard

"keccak@npm:3.0.2":
  version: 3.0.2
  resolution: "keccak@npm:3.0.2"
  dependencies:
    node-addon-api: "npm:^2.0.0"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.2.0"
    readable-stream: "npm:^3.6.0"
  checksum: 03f8d513040562f90ae892765431de29de0abf329dec40f1ef8b17eae634d56e283a7aeec5ae62e6ef96b9e8d1601329f2ef5c30a9d6c7baa6062d0f78d11b58
  languageName: node
  linkType: hard

"keccak@npm:^3.0.0, keccak@npm:^3.0.2":
  version: 3.0.4
  resolution: "keccak@npm:3.0.4"
  dependencies:
    node-addon-api: "npm:^2.0.0"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.2.0"
    readable-stream: "npm:^3.6.0"
  checksum: 45478bb0a57e44d0108646499b8360914b0fbc8b0e088f1076659cb34faaa9eb829c40f6dd9dadb3460bb86cc33153c41fed37fe5ce09465a60e71e78c23fa55
  languageName: node
  linkType: hard

"keyv@npm:^4.0.0, keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"klaw@npm:^1.0.0":
  version: 1.3.1
  resolution: "klaw@npm:1.3.1"
  dependencies:
    graceful-fs: "npm:^4.1.9"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 68b8ccb89f222dca60805df2b0e0fa0b3e4203ca1928b8facc0afac660e3e362809fe00f868ac877f495ebf89e376bb9ac9275508a132b5573e7382bed3ab006
  languageName: node
  linkType: hard

"latest-version@npm:^7.0.0":
  version: 7.0.0
  resolution: "latest-version@npm:7.0.0"
  dependencies:
    package-json: "npm:^8.1.0"
  checksum: 1f0deba00d5a34394cce4463c938811f51bbb539b131674f4bb2062c63f2cc3b80bccd56ecade3bd5932d04a34cf0a5a8a2ccc4ec9e5e6b285a9a7b3e27d0d66
  languageName: node
  linkType: hard

"level-codec@npm:~7.0.0":
  version: 7.0.1
  resolution: "level-codec@npm:7.0.1"
  checksum: 76a8a814cab0bdd45d10ee17f5a642c28996500de6684a08611925c4c342ace0587e028903afd35cc2991749c22196a735976743385f4a9dadd1273c97902420
  languageName: node
  linkType: hard

"level-errors@npm:^1.0.3":
  version: 1.1.2
  resolution: "level-errors@npm:1.1.2"
  dependencies:
    errno: "npm:~0.1.1"
  checksum: 18c22fd574ff31567642a85d9a306604a32cbe969b8469fee29620c10488214a6b5e6bbf19e3b5e2042859e4b81041af537319c18132a1aaa56d4ed5981157b7
  languageName: node
  linkType: hard

"level-errors@npm:~1.0.3":
  version: 1.0.5
  resolution: "level-errors@npm:1.0.5"
  dependencies:
    errno: "npm:~0.1.1"
  checksum: ae5d5c042c52975aa1543120fe5e1bf3d382c3f64aa9f036a616c6bf4b2de841eea4fc26107e96fe70ba34a21565e7902f9cafcf9ec699de0d98b02d53b42467
  languageName: node
  linkType: hard

"level-iterator-stream@npm:~1.3.0":
  version: 1.3.1
  resolution: "level-iterator-stream@npm:1.3.1"
  dependencies:
    inherits: "npm:^2.0.1"
    level-errors: "npm:^1.0.3"
    readable-stream: "npm:^1.0.33"
    xtend: "npm:^4.0.0"
  checksum: ed9a865cc02ff2edfbc231ab8bf911f4645c0571bfadc5c55a1893435a0c9990bbf8b0176c5f5838cf4117937c6b196e09d24a14da007c25c6c00c989e92fe72
  languageName: node
  linkType: hard

"level-supports@npm:^4.0.0":
  version: 4.0.1
  resolution: "level-supports@npm:4.0.1"
  checksum: e2f177af813a25af29d15406a14240e2e10e5efb1c35b03643c885ac5931af760b9337826506b6395f98cf6b1e68ba294bfc345a248a1ae3f9c69e08e81824b2
  languageName: node
  linkType: hard

"level-transcoder@npm:^1.0.1":
  version: 1.0.1
  resolution: "level-transcoder@npm:1.0.1"
  dependencies:
    buffer: "npm:^6.0.3"
    module-error: "npm:^1.0.1"
  checksum: 2fb41a1d8037fc279f851ead8cdc3852b738f1f935ac2895183cd606aae3e57008e085c7c2bd2b2d43cfd057333108cfaed604092e173ac2abdf5ab1b8333f9e
  languageName: node
  linkType: hard

"level-ws@npm:0.0.0":
  version: 0.0.0
  resolution: "level-ws@npm:0.0.0"
  dependencies:
    readable-stream: "npm:~1.0.15"
    xtend: "npm:~2.1.1"
  checksum: ee85b24c8fac8c48185af0dce6b87228c3ef941b2241793fe7201f7676a1588beb1cad5db1fbad75532628a28aaee50a4fbfd510c8da35faffc348a9fd05c272
  languageName: node
  linkType: hard

"level@npm:^8.0.0":
  version: 8.0.0
  resolution: "level@npm:8.0.0"
  dependencies:
    browser-level: "npm:^1.0.1"
    classic-level: "npm:^1.2.0"
  checksum: 1e7df97fe80fb158c8c1d6feeb651ee1381fd8e45af773b2bb02d3dd020fefd4f48a69d260b2d0ce9c4245ee9d8d40b8a9c49275b0b1ef6e1d4158feb5c39081
  languageName: node
  linkType: hard

"levelup@npm:^1.2.1":
  version: 1.3.9
  resolution: "levelup@npm:1.3.9"
  dependencies:
    deferred-leveldown: "npm:~1.2.1"
    level-codec: "npm:~7.0.0"
    level-errors: "npm:~1.0.3"
    level-iterator-stream: "npm:~1.3.0"
    prr: "npm:~1.0.1"
    semver: "npm:~5.4.1"
    xtend: "npm:~4.0.0"
  checksum: 2d7ce905a08b8073f3d012d66fdf7582111cc699da64772f0ebbb0dc225566674ef528d7167f6a7be9a0686a883623f638a9583c7b1a3e8cdcea40018666f2ca
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"locate-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "locate-path@npm:2.0.0"
  dependencies:
    p-locate: "npm:^2.0.0"
    path-exists: "npm:^3.0.0"
  checksum: 02d581edbbbb0fa292e28d96b7de36b5b62c2fa8b5a7e82638ebb33afa74284acf022d3b1e9ae10e3ffb7658fbc49163fcd5e76e7d1baaa7801c3e05a81da755
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: cd0b2819786e6e80cb9f5cda26b1a8fc073daaf04e48d4cb462fa4663ec9adb3a5387aa22d7129e48eed1afa05b482e2a6b79bfc99b86886364449500cbb00fd
  languageName: node
  linkType: hard

"lodash.truncate@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.truncate@npm:4.4.2"
  checksum: 7a495616121449e5d2288c606b1025d42ab9979e8c93ba885e5c5802ffd4f1ebad4428c793ccc12f73e73237e85a9f5b67dd6415757546fbd5a4653ba83e25ac
  languageName: node
  linkType: hard

"lodash@npm:^4.17.11, lodash@npm:^4.17.14, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"log-symbols@npm:4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: "npm:^4.1.0"
    is-unicode-supported: "npm:^0.1.0"
  checksum: fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"loupe@npm:^2.3.6":
  version: 2.3.7
  resolution: "loupe@npm:2.3.7"
  dependencies:
    get-func-name: "npm:^2.0.1"
  checksum: 635c8f0914c2ce7ecfe4e239fbaf0ce1d2c00e4246fafcc4ed000bfdb1b8f89d05db1a220054175cca631ebf3894872a26fffba0124477fcb562f78762848fb1
  languageName: node
  linkType: hard

"lowercase-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "lowercase-keys@npm:2.0.0"
  checksum: 1c233d2da35056e8c49fae8097ee061b8c799b2f02e33c2bf32f9913c7de8fb481ab04dab7df35e94156c800f5f34e99acbf32b21781d87c3aa43ef7b748b79e
  languageName: node
  linkType: hard

"lowercase-keys@npm:^3.0.0":
  version: 3.0.0
  resolution: "lowercase-keys@npm:3.0.0"
  checksum: 67a3f81409af969bc0c4ca0e76cd7d16adb1e25aa1c197229587eaf8671275c8c067cd421795dbca4c81be0098e4c426a086a05e30de8a9c587b7a13c0c7ccc5
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^9.1.1 || ^10.0.0":
  version: 10.1.0
  resolution: "lru-cache@npm:10.1.0"
  checksum: 207278d6fa711fb1f94a0835d4d4737441d2475302482a14785b10515e4c906a57ebf9f35bf060740c9560e91c7c1ad5a04fd7ed030972a9ba18bce2a228e95b
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 951d2673dcc64a7fb888bf3d13bc2fdf923faca97d89cdb405ba3dfff77e2b26e5798d405e78fcd7094c9e7b8b4dab2ddc5a4f8a11928af24a207b7c738ca3f8
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: fc1fe2ee205f7c8855fa0f34c1ab0bcf14b6229e35579ec1fd1079f31d6fc8ef8eb6fd17f2f4d99788d7e339f50e047555551ebd5e434dda503696e7c6591825
  languageName: node
  linkType: hard

"lru_map@npm:^0.3.3":
  version: 0.3.3
  resolution: "lru_map@npm:0.3.3"
  checksum: 50f6597924a7763ab0b31192e5e9965f08ca64a0044254138e74a65aecab95047d540f73739cff489866f4310e0202c11c10fdf18b10b236472160baaa68bbb1
  languageName: node
  linkType: hard

"ltgt@npm:~2.2.0":
  version: 2.2.1
  resolution: "ltgt@npm:2.2.1"
  checksum: 10536cee1d01114cf7aadd0c24fab432a4825bb8ef091488ae6d255df916ac7f15141f6bc1e023886aea0397353f0c14608581ce0dbb57f43704f77cc33731d0
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^13.0.0":
  version: 13.0.0
  resolution: "make-fetch-happen@npm:13.0.0"
  dependencies:
    "@npmcli/agent": "npm:^2.0.0"
    cacache: "npm:^18.0.0"
    http-cache-semantics: "npm:^4.1.1"
    is-lambda: "npm:^1.0.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^3.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^0.6.3"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^10.0.0"
  checksum: ded5a91a02b76381b06a4ec4d5c1d23ebbde15d402b3c3e4533b371dac7e2f7ca071ae71ae6dae72aa261182557b7b1b3fd3a705b39252dc17f74fa509d3e76f
  languageName: node
  linkType: hard

"match-all@npm:^1.2.6":
  version: 1.2.6
  resolution: "match-all@npm:1.2.6"
  checksum: f7e21e80aa2074b0140dcad6198145a9c89044bc164ab3365e7a5302bd180744c75bce53626aeec0753422ffead130d4142b0cd136f9cfff0eedb3227265ee3e
  languageName: node
  linkType: hard

"mcl-wasm@npm:^0.7.1":
  version: 0.7.9
  resolution: "mcl-wasm@npm:0.7.9"
  checksum: eb689cf0e2422ef7b98e8b040ed601821aea839718c876cd734e9148ca7013adf1c869bbc9495aac351e645d314ec3bd3d3612c91f60c499c5aea8d3dd2a7e38
  languageName: node
  linkType: hard

"md5.js@npm:^1.3.4":
  version: 1.3.5
  resolution: "md5.js@npm:1.3.5"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: 098494d885684bcc4f92294b18ba61b7bd353c23147fbc4688c75b45cb8590f5a95fd4584d742415dcc52487f7a1ef6ea611cfa1543b0dc4492fe026357f3f0c
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 38e0984db39139604756903a01397e29e17dcb04207bb3e081412ce725ab17338ecc47220c1b186b6bbe79a658aad1b0d41142884f5a481f36290cdefbe6aa46
  languageName: node
  linkType: hard

"memdown@npm:^1.0.0":
  version: 1.4.1
  resolution: "memdown@npm:1.4.1"
  dependencies:
    abstract-leveldown: "npm:~2.7.1"
    functional-red-black-tree: "npm:^1.0.1"
    immediate: "npm:^3.2.3"
    inherits: "npm:~2.0.1"
    ltgt: "npm:~2.2.0"
    safe-buffer: "npm:~5.1.1"
  checksum: 3f89142a12389b1ebfc7adaf3be19ed57cd073f84160eb7419b61c8e188e2b82eb787dad168d7b00ca68355b6b952067d9badaa5ac88c8ee014e4b0af2bfaea0
  languageName: node
  linkType: hard

"memory-level@npm:^1.0.0":
  version: 1.0.0
  resolution: "memory-level@npm:1.0.0"
  dependencies:
    abstract-level: "npm:^1.0.0"
    functional-red-black-tree: "npm:^1.0.1"
    module-error: "npm:^1.0.1"
  checksum: e3293d8c67ebc0aa4b29982c5f8e3d139c5b1b04b97fa3ae98f940f91c7bdfefec9ff189742943734ebb6c7efa85fed6a4d559407b2d5751106b24cac17a23a6
  languageName: node
  linkType: hard

"memorystream@npm:^0.3.1":
  version: 0.3.1
  resolution: "memorystream@npm:0.3.1"
  checksum: 2e34a1e35e6eb2e342f788f75f96c16f115b81ff6dd39e6c2f48c78b464dbf5b1a4c6ebfae4c573bd0f8dbe8c57d72bb357c60523be184655260d25855c03902
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-descriptors@npm:1.0.1"
  checksum: 5abc259d2ae25bb06d19ce2b94a21632583c74e2a9109ee1ba7fd147aa7362b380d971e0251069f8b3eb7d48c21ac839e21fa177b335e82c76ec172e30c31a26
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"merkle-patricia-tree@npm:^2.1.2, merkle-patricia-tree@npm:^2.3.2":
  version: 2.3.2
  resolution: "merkle-patricia-tree@npm:2.3.2"
  dependencies:
    async: "npm:^1.4.2"
    ethereumjs-util: "npm:^5.0.0"
    level-ws: "npm:0.0.0"
    levelup: "npm:^1.2.1"
    memdown: "npm:^1.0.0"
    readable-stream: "npm:^2.0.0"
    rlp: "npm:^2.0.0"
    semaphore: "npm:>=1.0.1"
  checksum: 4188bd5b163719bd9c5da45ba356fac753a0f0ac0b9990923b5f064852e083334abd5e4d838dcd35ee50d884dad5c52adbd103387c89d20be3ec47b51784946a
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: a385dd974faa34b5dd021b2bbf78c722881bf6f003bfe6d391d7da3ea1ed625d1ff10ddd13c57531f628b3e785be38d3eed10ad03cebd90b76932413df9a1820
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: "npm:^3.0.2"
    picomatch: "npm:^2.3.1"
  checksum: a749888789fc15cac0e03273844dbd749f9f8e8d64e70c564bcf06a033129554c789bb9e30d7566d7ff6596611a08e58ac12cf2a05f6e3c9c47c50c4c7e12fa2
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.16, mime-types@npm:~2.1.19, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: b7d98bb1e006c0e63e2c91b590fe1163b872abf8f7ef224d53dd31499c2197278a6d3d0864c45239b1a93d22feaf6f9477e9fc847eef945838150b8c02d03170
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-fn@npm:4.0.0"
  checksum: 995dcece15ee29aa16e188de6633d43a3db4611bcf93620e7e62109ec41c79c0f34277165b8ce5e361205049766e371851264c21ac64ca35499acb5421c2ba56
  languageName: node
  linkType: hard

"mimic-response@npm:^1.0.0":
  version: 1.0.1
  resolution: "mimic-response@npm:1.0.1"
  checksum: 034c78753b0e622bc03c983663b1cdf66d03861050e0c8606563d149bc2b02d63f62ce4d32be4ab50d0553ae0ffe647fc34d1f5281184c6e1e8cf4d85e8d9823
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 7e719047612411fe071332a7498cf0448bbe43c485c0d780046c76633a771b223ff49bd00267be122cedebb897037fdb527df72335d0d0f74724604ca70b37ad
  languageName: node
  linkType: hard

"mimic-response@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-response@npm:4.0.0"
  checksum: 33b804cc961efe206efdb1fca6a22540decdcfce6c14eb5c0c50e5ae9022267ab22ce8f5568b1f7247ba67500fe20d523d81e0e9f009b321ccd9d472e78d1850
  languageName: node
  linkType: hard

"min-document@npm:^2.19.0":
  version: 2.19.0
  resolution: "min-document@npm:2.19.0"
  dependencies:
    dom-walk: "npm:^0.1.0"
  checksum: 4e45a0686c81cc04509989235dc6107e2678a59bb48ce017d3c546d7d9a18d782e341103e66c78081dd04544704e2196e529905c41c2550bca069b69f95f07c8
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 6e8a0422b30039406efd4c440829ea8f988845db02a3299f372fceba56ffa94994a9c0f2fd70c17f9969eedfbd72f34b5070ead9656a34d3f71c0bd72583a0ed
  languageName: node
  linkType: hard

"minimatch@npm:5.0.1":
  version: 5.0.1
  resolution: "minimatch@npm:5.0.1"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 2656580f18d9f38ada186196fcc72dc9076d70f7227adc664e72614d464e075dc4ae3936e6742519e09e336996ef33c6035e606888b12f65ca7fda792ddd2085
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 126b36485b821daf96d33b5c821dac600cc1ab36c87e7a532594f9b1652b1fa89a1eebcaad4dff17c764dce1a7ac1531327f190fed5f97d8f6e5f889c116c429
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.1":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: c81b47d28153e77521877649f4bab48348d10938df9e8147a58111fe00ef89559a2938de9f6632910c4f7bf7bb5cd81191a546167e58d357f0cfb1e18cecc1c5
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 908491b6cc15a6c440ba5b22780a0ba89b9810e1aea684e253e43c4e3b8d56ec1dcdd7ea96dde119c29df59c936cde16062159eae4225c691e19c70b432b6e6f
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.4
  resolution: "minipass-fetch@npm:3.0.4"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^2.1.2"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3edf72b900e30598567eafe96c30374432a8709e61bb06b87198fa3192d466777e2ec21c52985a0999044fa6567bd6f04651585983a1cbb27e2c1770a07ed2a2
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^2.6.0, minipass@npm:^2.9.0":
  version: 2.9.0
  resolution: "minipass@npm:2.9.0"
  dependencies:
    safe-buffer: "npm:^5.1.2"
    yallist: "npm:^3.0.0"
  checksum: fdd1a77996c184991f8d2ce7c5b3979bec624e2a3225e2e1e140c4038fd65873d7eb90fb29779f8733735a8827b2686f283871a0c74c908f4f7694c56fa8dadf
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 61682162d29f45d3152b78b08bab7fb32ca10899bc5991ffe98afc18c9e9543bd1e3be94f8b8373ba6262497db63607079dc242ea62e43e7b2270837b7347c93
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3":
  version: 7.0.4
  resolution: "minipass@npm:7.0.4"
  checksum: e864bd02ceb5e0707696d58f7ce3a0b89233f0d686ef0d447a66db705c0846a8dc6f34865cd85256c1472ff623665f616b90b8ff58058b2ad996c5de747d2d18
  languageName: node
  linkType: hard

"minizlib@npm:^1.3.3":
  version: 1.3.3
  resolution: "minizlib@npm:1.3.3"
  dependencies:
    minipass: "npm:^2.9.0"
  checksum: 9c2c47e5687d7f896431a9b5585988ef72f848b56c6a974c9489534e8f619388d500d986ef82e1c13aedd46f3a0e81b6a88110cb1b27de7524cc8dabe8885e17
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: ae0f45436fb51344dcb87938446a32fbebb540d0e191d63b35e1c773d47512e17307bf54aa88326cc6d176594d00e4423563a091f7266c2f9a6872cdc1e234d1
  languageName: node
  linkType: hard

"mkdirp-promise@npm:^5.0.1":
  version: 5.0.1
  resolution: "mkdirp-promise@npm:5.0.1"
  dependencies:
    mkdirp: "npm:*"
  checksum: 31ddc9478216adf6d6bee9ea7ce9ccfe90356d9fcd1dfb18128eac075390b4161356d64c3a7b0a75f9de01a90aadd990a0ec8c7434036563985c4b853a053ee2
  languageName: node
  linkType: hard

"mkdirp@npm:*":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.5":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 0c91b721bb12c3f9af4b77ebf73604baf350e64d80df91754dc509491ae93bf238581e59c7188360cec7cb62fc4100959245a42cfe01834efedc5e9d068376c2
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: d71b8dcd4b5af2fe13ecf3bd24070263489404fe216488c5ba7e38ece1f54daf219e72a833a3a2dc404331e870e9f44963a33399589490956bff003a3404d3b2
  languageName: node
  linkType: hard

"mnemonist@npm:^0.38.0":
  version: 0.38.5
  resolution: "mnemonist@npm:0.38.5"
  dependencies:
    obliterator: "npm:^2.0.0"
  checksum: 2df34862567376acb8c2411d546ba9f109229acb2b7fe7593df6fe62194d98f124cf7ff7b2d6f457a3f0410d4d8b44389022ac853d5e5448a2603c4b12f733bf
  languageName: node
  linkType: hard

"mocha@npm:^10.0.0":
  version: 10.2.0
  resolution: "mocha@npm:10.2.0"
  dependencies:
    ansi-colors: "npm:4.1.1"
    browser-stdout: "npm:1.3.1"
    chokidar: "npm:3.5.3"
    debug: "npm:4.3.4"
    diff: "npm:5.0.0"
    escape-string-regexp: "npm:4.0.0"
    find-up: "npm:5.0.0"
    glob: "npm:7.2.0"
    he: "npm:1.2.0"
    js-yaml: "npm:4.1.0"
    log-symbols: "npm:4.1.0"
    minimatch: "npm:5.0.1"
    ms: "npm:2.1.3"
    nanoid: "npm:3.3.3"
    serialize-javascript: "npm:6.0.0"
    strip-json-comments: "npm:3.1.1"
    supports-color: "npm:8.1.1"
    workerpool: "npm:6.2.1"
    yargs: "npm:16.2.0"
    yargs-parser: "npm:20.2.4"
    yargs-unparser: "npm:2.0.0"
  bin:
    _mocha: bin/_mocha
    mocha: bin/mocha.js
  checksum: f7362898ae65e8fe716cfe62fd014b432d100c9611aaf5abe85ed14efcbfdd82f3bdf32c44bccf00c9059a264c7e8d93a69dd5b830652109052a92beffb7ea35
  languageName: node
  linkType: hard

"mock-fs@npm:^4.1.0":
  version: 4.14.0
  resolution: "mock-fs@npm:4.14.0"
  checksum: 20facbc85bb62df02dbfc946b354fcdd8b2b2aeafef4986adab18dc9a23efccb34ce49d4dac22aaed1a24420fc50c53d77e90984cc888bcce314e18e0e21872a
  languageName: node
  linkType: hard

"module-error@npm:^1.0.1, module-error@npm:^1.0.2":
  version: 1.0.2
  resolution: "module-error@npm:1.0.2"
  checksum: 5d653e35bd55b3e95f8aee2cdac108082ea892e71b8f651be92cde43e4ee86abee4fa8bd7fc3fe5e68b63926d42f63c54cd17b87a560c31f18739295575a3962
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"multibase@npm:^0.7.0":
  version: 0.7.0
  resolution: "multibase@npm:0.7.0"
  dependencies:
    base-x: "npm:^3.0.8"
    buffer: "npm:^5.5.0"
  checksum: a5cbbf00b8aa61bcb92a706e210d8f258e8413cff2893584fedbc316c98bf2a44b8f648b57c124ddfaa29750c3b686ee5ba973cb8da84a896c19d63101b09445
  languageName: node
  linkType: hard

"multibase@npm:~0.6.0":
  version: 0.6.1
  resolution: "multibase@npm:0.6.1"
  dependencies:
    base-x: "npm:^3.0.8"
    buffer: "npm:^5.5.0"
  checksum: c9e3bf20dc1b109019b94b14a76731ea0a6b0e654a4ef627ba154bfc2b8602ac43b160c44d8245d18cd6a9ed971826efb204230f22b929c8b3e72da13dbc1859
  languageName: node
  linkType: hard

"multicodec@npm:^0.5.5":
  version: 0.5.7
  resolution: "multicodec@npm:0.5.7"
  dependencies:
    varint: "npm:^5.0.0"
  checksum: b61bbf04e1bfff180f77693661b8111bf94f65580abc455e6d83d2240c227d8c2e8af99ca93b6c02500c5da43d16e2b028dbbec1b376a85145a774f542d9ca2c
  languageName: node
  linkType: hard

"multicodec@npm:^1.0.0":
  version: 1.0.4
  resolution: "multicodec@npm:1.0.4"
  dependencies:
    buffer: "npm:^5.6.0"
    varint: "npm:^5.0.0"
  checksum: 3a78ac54d3715e6b095a1805f63b4c4e7d5bb4642445691c0c4e6442cad9f97823469634e73ee362ba748596570db1050d69d5cc74a88928b1e9658916cdfbcd
  languageName: node
  linkType: hard

"multihashes@npm:^0.4.15, multihashes@npm:~0.4.15":
  version: 0.4.21
  resolution: "multihashes@npm:0.4.21"
  dependencies:
    buffer: "npm:^5.5.0"
    multibase: "npm:^0.7.0"
    varint: "npm:^5.0.0"
  checksum: a482d9ba7ed0ad41db22ca589f228e4b7a30207a229a64dfc9888796752314fca00a8d03025fe40d6d73965bbb246f54b73626c5a235463e30c06c7bf7a8785f
  languageName: node
  linkType: hard

"murmur-128@npm:^0.2.1":
  version: 0.2.1
  resolution: "murmur-128@npm:0.2.1"
  dependencies:
    encode-utf8: "npm:^1.0.2"
    fmix: "npm:^0.1.0"
    imul: "npm:^1.0.0"
  checksum: 0ec68c6d2176f1361699585ea54562ed3fe7a9260841cd58e39fdab2e2da5bc856ee9c9df3c5ae02d1cf9cd14432c24c8b70f80e64a69ab3b3484808539b5e83
  languageName: node
  linkType: hard

"nano-json-stream-parser@npm:^0.1.2":
  version: 0.1.2
  resolution: "nano-json-stream-parser@npm:0.1.2"
  checksum: 00a3ce63d3b66220def9fd6c26cd495100efd155e7bda54a11f1dfd185ba6750d5ce266076e0f229bad3f5ef892e2017f24da012669f146b404a8e47a44568ec
  languageName: node
  linkType: hard

"nanoid@npm:3.3.3":
  version: 3.3.3
  resolution: "nanoid@npm:3.3.3"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: c703ed58a234b68245a8a4826dd25c1453a9017d34fa28bc58e7aa8247de87d854582fa2209d7aee04084cff9ce150be8fd30300abe567dc615d4e8e735f2d99
  languageName: node
  linkType: hard

"napi-macros@npm:^2.2.2":
  version: 2.2.2
  resolution: "napi-macros@npm:2.2.2"
  checksum: 2cdb9c40ad4b424b14fbe5e13c5329559e2b511665acf41cdcda172fd2270202dc747a2d288b687c72bc70f654c797bc24a93adb67631128d62461588d7cc070
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3, negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 2723fb822a17ad55c93a588a4bc44d53b22855bf4be5499916ca0cab1e7165409d0b288ba2577d7b029f10ce18cf2ed8e703e5af31c984e1e2304277ef979837
  languageName: node
  linkType: hard

"next-tick@npm:^1.1.0":
  version: 1.1.0
  resolution: "next-tick@npm:1.1.0"
  checksum: 83b5cf36027a53ee6d8b7f9c0782f2ba87f4858d977342bfc3c20c21629290a2111f8374d13a81221179603ffc4364f38374b5655d17b6a8f8a8c77bdea4fe8b
  languageName: node
  linkType: hard

"node-addon-api@npm:^2.0.0":
  version: 2.0.2
  resolution: "node-addon-api@npm:2.0.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: e4ce4daac5b2fefa6b94491b86979a9c12d9cceba571d2c6df1eb5859f9da68e5dc198f128798e1785a88aafee6e11f4992dcccd4bf86bec90973927d158bd60
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.0, node-fetch@npm:^2.6.1, node-fetch@npm:^2.6.12, node-fetch@npm:^2.6.7":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: b24f8a3dc937f388192e59bcf9d0857d7b6940a2496f328381641cb616efccc9866e89ec43f2ec956bbd6c3d3ee05524ce77fe7b29ccd34692b3a16f237d6676
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.2.0, node-gyp-build@npm:^4.3.0":
  version: 4.7.1
  resolution: "node-gyp-build@npm:4.7.1"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 3f6780a24dc7f6c47870ee1095a3f88aca9ca9c156dfdc390aee8f320fe94ebf8b91a361edd62aff7bf2eae469e25800378ed97533134d8580a8b9bdae75994c
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 10.0.1
  resolution: "node-gyp@npm:10.0.1"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^13.0.0"
    nopt: "npm:^7.0.0"
    proc-log: "npm:^3.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^6.1.2"
    which: "npm:^4.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 578cf0c821f258ce4b6ebce4461eca4c991a4df2dee163c0624f2fe09c7d6d37240be4942285a0048d307230248ee0b18382d6623b9a0136ce9533486deddfa8
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.14":
  version: 2.0.14
  resolution: "node-releases@npm:2.0.14"
  checksum: 0f7607ec7db5ef1dc616899a5f24ae90c869b6a54c2d4f36ff6d84a282ab9343c7ff3ca3670fe4669171bb1e8a9b3e286e1ef1c131f09a83d70554f855d54f24
  languageName: node
  linkType: hard

"nopt@npm:^7.0.0":
  version: 7.2.0
  resolution: "nopt@npm:7.2.0"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 1e7489f17cbda452c8acaf596a8defb4ae477d2a9953b76eb96f4ec3f62c6b421cd5174eaa742f88279871fde9586d8a1d38fb3f53fa0c405585453be31dff4c
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 5ae699402c9d5ffa330adc348fcd6fc6e6a155ab7c811b96e30b7ecab60ceef821d8f86443869671dda71bbc47f4b9625739c82ad247e883e9aefe875bfb8659
  languageName: node
  linkType: hard

"normalize-url@npm:^8.0.0":
  version: 8.0.0
  resolution: "normalize-url@npm:8.0.0"
  checksum: 4347d6ee39d9e1e7138c9e7c0b459c1e07304d9cd7c62d92c1ca01ed1f0c5397b292079fe7cfa953f469722ae150eec82e14b97e2175af39ede0b58f99ef8cac
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"npm-run-path@npm:^5.1.0":
  version: 5.1.0
  resolution: "npm-run-path@npm:5.1.0"
  dependencies:
    path-key: "npm:^4.0.0"
  checksum: dc184eb5ec239d6a2b990b43236845332ef12f4e0beaa9701de724aa797fe40b6bbd0157fb7639d24d3ab13f5d5cf22d223a19c6300846b8126f335f788bee66
  languageName: node
  linkType: hard

"number-to-bn@npm:1.7.0":
  version: 1.7.0
  resolution: "number-to-bn@npm:1.7.0"
  dependencies:
    bn.js: "npm:4.11.6"
    strip-hex-prefix: "npm:1.0.0"
  checksum: 702e8f00b6b90abd23f711056005179c3bd5ce3b063c47d468250f63ab3b9b4b82e27bff3b4642a9e71e06c717d5ed359873501746df0a64c3db1fa6d704e704
  languageName: node
  linkType: hard

"oauth-sign@npm:~0.9.0":
  version: 0.9.0
  resolution: "oauth-sign@npm:0.9.0"
  checksum: 1809a366d258f41fdf4ab5310cff3d1e15f96b187503bc7333cef4351de7bd0f52cb269bc95800f1fae5fb04dd886287df1471985fd67e8484729fdbcf857119
  languageName: node
  linkType: hard

"object-assign@npm:^4, object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.9.0":
  version: 1.13.1
  resolution: "object-inspect@npm:1.13.1"
  checksum: 92f4989ed83422d56431bc39656d4c780348eb15d397ce352ade6b7fec08f973b53744bd41b94af021901e61acaf78fcc19e65bf464ecc0df958586a672700f0
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 3d81d02674115973df0b7117628ea4110d56042e5326413e4b4313f0bcdf7dd78d4a3acef2c831463fa3796a66762c49daef306f4a0ea1af44877d7086d73bde
  languageName: node
  linkType: hard

"object-keys@npm:~0.4.0":
  version: 0.4.0
  resolution: "object-keys@npm:0.4.0"
  checksum: 9ab0a2c3d922f7f18d00c1b1646c3a4b686bebeecdd90531bbe5bbbc72a381313b9e43ff0d9cf9b5b212906a19fd02195d013ca5d67d42758e72e5d2a79668d0
  languageName: node
  linkType: hard

"obliterator@npm:^2.0.0":
  version: 2.0.4
  resolution: "obliterator@npm:2.0.4"
  checksum: 5a49ce3736aa9c8ae536e14e556e347b225c71215d3d3e0b191da0386284a804b9e22c09780645f2cea3981d4cecefaa394f59f4ffd6167fe6c2f2401777e1ae
  languageName: node
  linkType: hard

"oboe@npm:2.1.5":
  version: 2.1.5
  resolution: "oboe@npm:2.1.5"
  dependencies:
    http-https: "npm:^1.0.0"
  checksum: 451d0c28b45f518fc86d4689075cf74c7fea92fb09e2f994dd1208e5c5516a6958f9dc476714b61c62c959a3e7e0db8a69999c59ff63777c7a8af24fbddd0848
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 8e81472c5028125c8c39044ac4ab8ba51a7cdc19a9fbd4710f5d524a74c6d8c9ded4dd0eed83f28d3d33ac1d7a6a439ba948ccb765ac6ce87f30450a26bfe2ea
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: e9fd0695a01cf226652f0385bf16b7a24153dbbb2039f764c8ba6d2306a8506b0e4ce570de6ad99c7a6eb49520743afdb66edd95ee979c1a342554ed49a9aadd
  languageName: node
  linkType: hard

"onetime@npm:^6.0.0":
  version: 6.0.0
  resolution: "onetime@npm:6.0.0"
  dependencies:
    mimic-fn: "npm:^4.0.0"
  checksum: 0846ce78e440841335d4e9182ef69d5762e9f38aa7499b19f42ea1c4cd40f0b4446094c455c713f9adac3f4ae86f613bb5e30c99e52652764d06a89f709b3788
  languageName: node
  linkType: hard

"open@npm:^9.1.0":
  version: 9.1.0
  resolution: "open@npm:9.1.0"
  dependencies:
    default-browser: "npm:^4.0.0"
    define-lazy-prop: "npm:^3.0.0"
    is-inside-container: "npm:^1.0.0"
    is-wsl: "npm:^2.2.0"
  checksum: b45bcc7a6795804a2f560f0ca9f5e5344114bc40754d10c28a811c0c8f7027356979192931a6a7df2ab9e5bab3058988c99ae55f4fb71db2ce9fc77c40f619aa
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"p-cancelable@npm:^2.0.0":
  version: 2.1.1
  resolution: "p-cancelable@npm:2.1.1"
  checksum: 7f1b64db17fc54acf359167d62898115dcf2a64bf6b3b038e4faf36fc059e5ed762fb9624df8ed04b25bee8de3ab8d72dea9879a2a960cd12e23c420a4aca6ed
  languageName: node
  linkType: hard

"p-cancelable@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-cancelable@npm:3.0.0"
  checksum: a5eab7cf5ac5de83222a014eccdbfde65ecfb22005ee9bc242041f0b4441e07fac7629432c82f48868aa0f8413fe0df6c6067c16f76bf9217cd8dc651923c93d
  languageName: node
  linkType: hard

"p-limit@npm:^1.1.0":
  version: 1.3.0
  resolution: "p-limit@npm:1.3.0"
  dependencies:
    p-try: "npm:^1.0.0"
  checksum: eb9d9bc378d48ab1998d2a2b2962a99eddd3e3726c82d3258ecc1a475f22907968edea4fec2736586d100366a001c6bb449a2abe6cd65e252e9597394f01e789
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^2.0.0":
  version: 2.0.0
  resolution: "p-locate@npm:2.0.0"
  dependencies:
    p-limit: "npm:^1.1.0"
  checksum: e2dceb9b49b96d5513d90f715780f6f4972f46987dc32a0e18bc6c3fc74a1a5d73ec5f81b1398af5e58b99ea1ad03fd41e9181c01fa81b4af2833958696e3081
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 7ba4a2b1e24c05e1fc14bbaea0fc6d85cf005ae7e9c9425d4575550f37e2e584b1af97bcde78eacd7559208f20995988d52881334db16cf77bc1bcf68e48ed7c
  languageName: node
  linkType: hard

"p-try@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-try@npm:1.0.0"
  checksum: 20d9735f57258158df50249f172c77fe800d31e80f11a3413ac9e68ccbe6b11798acb3f48f2df8cea7ba2b56b753ce695a4fe2a2987c3c7691c44226b6d82b6f
  languageName: node
  linkType: hard

"package-json@npm:^8.1.0":
  version: 8.1.1
  resolution: "package-json@npm:8.1.1"
  dependencies:
    got: "npm:^12.1.0"
    registry-auth-token: "npm:^5.0.1"
    registry-url: "npm:^6.0.0"
    semver: "npm:^7.3.7"
  checksum: d97ce9539e1ed4aacaf7c2cb754f16afc10937fa250bd09b4d61181d2e36a30cf8a4cff2f8f831f0826b0ac01a355f26204c7e57ca0e450da6ccec3e34fc889a
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-headers@npm:^2.0.0":
  version: 2.0.5
  resolution: "parse-headers@npm:2.0.5"
  checksum: 210b13bc0f99cf6f1183896f01de164797ac35b2720c9f1c82a3e2ceab256f87b9048e8e16a14cfd1b75448771f8379cd564bd1674a179ab0168c90005d4981b
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 96e92643aa34b4b28d0de1cd2eba52a1c5313a90c6542d03f62750d82480e20bfa62bc865d5cfc6165f5fcd5aeb0851043c40a39be5989646f223300021bae0a
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 8e6c314ae6d16b83e93032c61020129f6f4484590a777eed709c4a01b50e498822b00f76ceaf94bc64dbd90b327df56ceadce27da3d83393790f1219e07721d7
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.6, path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.10.1":
  version: 1.10.1
  resolution: "path-scurry@npm:1.10.1"
  dependencies:
    lru-cache: "npm:^9.1.1 || ^10.0.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: eebfb8304fef1d4f7e1486df987e4fd77413de4fce16508dea69fcf8eb318c09a6b15a7a2f4c22877cec1cb7ecbd3071d18ca9de79eeece0df874a00f1f0bdc8
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.7":
  version: 0.1.7
  resolution: "path-to-regexp@npm:0.1.7"
  checksum: 701c99e1f08e3400bea4d701cf6f03517474bb1b608da71c78b1eb261415b645c5670dfae49808c89e12cea2dccd113b069f040a80de012da0400191c6dbd1c8
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"pathval@npm:^1.1.1":
  version: 1.1.1
  resolution: "pathval@npm:1.1.1"
  checksum: b50a4751068aa3a5428f5a0b480deecedc6f537666a3630a0c2ae2d5e7c0f4bf0ee77b48404441ec1220bef0c91625e6030b3d3cf5a32ab0d9764018d1d9dbb6
  languageName: node
  linkType: hard

"pbkdf2@npm:^3.0.17":
  version: 3.1.2
  resolution: "pbkdf2@npm:3.1.2"
  dependencies:
    create-hash: "npm:^1.1.2"
    create-hmac: "npm:^1.1.4"
    ripemd160: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 40bdf30df1c9bb1ae41ec50c11e480cf0d36484b7c7933bf55e4451d1d0e3f09589df70935c56e7fccc5702779a0d7b842d012be8c08a187b44eb24d55bb9460
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 534e641aa8f7cba160f0afec0599b6cecefbb516a2e837b512be0adbe6c1da5550e89c78059c7fabc5c9ffdf6627edabe23eb7c518c4500067a898fa65c2b550
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 668c1dc8d9fc1b34b9ce3b16ba59deb39d4dc743527bf2ed908d2b914cb8ba40aa5ba6960b27c417c241531c5aafd0598feeac2d50cb15278cf9863fa6b02a77
  languageName: node
  linkType: hard

"pify@npm:^5.0.0":
  version: 5.0.0
  resolution: "pify@npm:5.0.0"
  checksum: 443e3e198ad6bfa8c0c533764cf75c9d5bc976387a163792fb553ffe6ce923887cf14eebf5aea9b7caa8eab930da8c33612990ae85bd8c2bc18bedb9eae94ecb
  languageName: node
  linkType: hard

"pluralize@npm:^8.0.0":
  version: 8.0.0
  resolution: "pluralize@npm:8.0.0"
  checksum: 17877fdfdb7ddb3639ce257ad73a7c51a30a966091e40f56ea9f2f545b5727ce548d4928f8cb3ce38e7dc0c5150407d318af6a4ed0ea5265d378473b4c2c61ec
  languageName: node
  linkType: hard

"precond@npm:0.2":
  version: 0.2.3
  resolution: "precond@npm:0.2.3"
  checksum: d5215e17cc812996f72ee57a684ff159709bdfa48538e71c361d17aecd750bf25f64f85ba2c49031860708e0e70ac4394b9c8280725f227b88bce0fe76f8389a
  languageName: node
  linkType: hard

"prettier-plugin-packagejson@npm:^2.4.7":
  version: 2.4.7
  resolution: "prettier-plugin-packagejson@npm:2.4.7"
  dependencies:
    sort-package-json: "npm:2.6.0"
    synckit: "npm:0.8.6"
  peerDependencies:
    prettier: ">= 1.16.0"
  peerDependenciesMeta:
    prettier:
      optional: true
  checksum: 54211cb68e1136e4b1bc8bd02c939f652cb7a9d4d8f5721ceab0b18ed592f8f4d3f47ab4b6610377ad78704762056a7402bd42dc1500ad9569f58ce41a418b26
  languageName: node
  linkType: hard

"prettier-plugin-solidity@npm:^1.2.0":
  version: 1.2.0
  resolution: "prettier-plugin-solidity@npm:1.2.0"
  dependencies:
    "@solidity-parser/parser": "npm:^0.16.2"
    semver: "npm:^7.5.4"
    solidity-comments-extractor: "npm:^0.0.7"
  peerDependencies:
    prettier: ">=2.3.0"
  checksum: 5b9a77ae33292b6093f5dfcbeb9948dbaa0f7f89917a4be5940f0d29a6103238bc7c5d61919e15ab9fa92aeefe7c86fb7870e6421353b984b9ec71ab4c00ebf2
  languageName: node
  linkType: hard

"prettier@npm:^2.8.3":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: 00cdb6ab0281f98306cd1847425c24cbaaa48a5ff03633945ab4c701901b8e96ad558eb0777364ffc312f437af9b5a07d0f45346266e8245beaf6247b9c62b24
  languageName: node
  linkType: hard

"prettier@npm:^3.1.0":
  version: 3.1.1
  resolution: "prettier@npm:3.1.1"
  bin:
    prettier: bin/prettier.cjs
  checksum: 26a249f321b97d26c04483f1bf2eeb22e082a76f4222a2c922bebdc60111691aad4ec3979610e83942e0b956058ec361d9e9c81c185172264eb6db9aa678082b
  languageName: node
  linkType: hard

"proc-log@npm:^3.0.0":
  version: 3.0.0
  resolution: "proc-log@npm:3.0.0"
  checksum: 02b64e1b3919e63df06f836b98d3af002b5cd92655cab18b5746e37374bfb73e03b84fe305454614b34c25b485cc687a9eebdccf0242cda8fda2475dd2c97e02
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: dbaa7e8d1d5cf375c36963ff43116772a989ef2bb47c9bdee20f38fd8fc061119cf38140631cf90c781aca4d3f0f0d2c834711952b728953f04fd7d238f59f5b
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"promise-to-callback@npm:^1.0.0":
  version: 1.0.0
  resolution: "promise-to-callback@npm:1.0.0"
  dependencies:
    is-fn: "npm:^1.0.0"
    set-immediate-shim: "npm:^1.0.1"
  checksum: d756ad74f7a202a6725c4281813c1052896327e4036c553adb8e3bbb91d0b1ff501965e7b00997827c016609caefbf24a5efc117dccca6000b8cbbc477b31cbf
  languageName: node
  linkType: hard

"proto-list@npm:~1.2.1":
  version: 1.2.4
  resolution: "proto-list@npm:1.2.4"
  checksum: 9cc3b46d613fa0d637033b225db1bc98e914c3c05864f7adc9bee728192e353125ef2e49f71129a413f6333951756000b0e54f299d921f02d3e9e370cc994100
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: f24a0c80af0e75d31e3451398670d73406ec642914da11a2965b80b1898ca6f66a0e3e091a11a4327079b2b268795f6fa06691923fef91887215c3d0e8ea3f68
  languageName: node
  linkType: hard

"prr@npm:~1.0.1":
  version: 1.0.1
  resolution: "prr@npm:1.0.1"
  checksum: 3bca2db0479fd38f8c4c9439139b0c42dcaadcc2fbb7bb8e0e6afaa1383457f1d19aea9e5f961d5b080f1cfc05bfa1fe9e45c97a1d3fd6d421950a73d3108381
  languageName: node
  linkType: hard

"psl@npm:^1.1.28":
  version: 1.9.0
  resolution: "psl@npm:1.9.0"
  checksum: d07879d4bfd0ac74796306a8e5a36a93cfb9c4f4e8ee8e63fbb909066c192fe1008cd8f12abd8ba2f62ca28247949a20c8fb32e1d18831d9e71285a1569720f9
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: e42e9229fba14732593a718b04cb5e1cfef8254544870997e0ecd9732b189a48e1256e4e5478148ecb47c8511dca2b09eae56b4d0aad8009e6fac8072923cfc9
  languageName: node
  linkType: hard

"punycode@npm:2.1.0":
  version: 2.1.0
  resolution: "punycode@npm:2.1.0"
  checksum: 012f9443fe56baf485db702d0d07cef7d89c0670ce1ac4da8fb8b5bd3677e42a8f5d2b35f595ffa31ba843661c9c6766f2feb1e1e3393e1ff1033120d0f94d60
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"qs@npm:6.11.0":
  version: 6.11.0
  resolution: "qs@npm:6.11.0"
  dependencies:
    side-channel: "npm:^1.0.4"
  checksum: 5a3bfea3e2f359ede1bfa5d2f0dbe54001aa55e40e27dc3e60fab814362d83a9b30758db057c2011b6f53a2d4e4e5150194b5bac45372652aecb3e3c0d4b256e
  languageName: node
  linkType: hard

"qs@npm:^6.9.4":
  version: 6.11.2
  resolution: "qs@npm:6.11.2"
  dependencies:
    side-channel: "npm:^1.0.4"
  checksum: f2321d0796664d0f94e92447ccd3bdfd6b6f3a50b6b762aa79d7f5b1ea3a7a9f94063ba896b82bc2a877ed6a7426d4081e4f16568fdb04f0ee188cca9d8505b4
  languageName: node
  linkType: hard

"qs@npm:~6.5.2":
  version: 6.5.3
  resolution: "qs@npm:6.5.3"
  checksum: 485c990fba7ad17671e16c92715fb064c1600337738f5d140024eb33a49fbc1ed31890d3db850117c760caeb9c9cc9f4ba22a15c20dd119968e41e3d3fe60b28
  languageName: node
  linkType: hard

"query-string@npm:^5.0.1":
  version: 5.1.1
  resolution: "query-string@npm:5.1.1"
  dependencies:
    decode-uri-component: "npm:^0.2.0"
    object-assign: "npm:^4.1.0"
    strict-uri-encode: "npm:^1.0.0"
  checksum: 8834591ed02c324ac10397094c2ae84a3d3460477ef30acd5efe03b1afbf15102ccc0829ab78cc58ecb12f70afeb7a1f81e604487a9ad4859742bb14748e98cc
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2, queue-microtask@npm:^1.2.3":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: a516faa25574be7947969883e6068dbe4aa19e8ef8e8e0fd96cddd6d36485e9106d85c0041a27153286b0770b381328f4072aa40d3b18a19f5f7d2b78b94b5ed
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 4efd1ad3d88db77c2d16588dc54c2b52fd2461e70fe5724611f38d283857094fe09040fa2c9776366803c3152cf133171b452ef717592b65631ce5dc3a2bdafc
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: ce21ef2a2dd40506893157970dc76e835c78cf56437e26e19189c48d5291e7279314477b06ac38abd6a401b661a6840f7b03bd0b1249da9b691deeaa15872c26
  languageName: node
  linkType: hard

"raw-body@npm:2.5.1":
  version: 2.5.1
  resolution: "raw-body@npm:2.5.1"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 280bedc12db3490ecd06f740bdcf66093a07535374b51331242382c0e130bb273ebb611b7bc4cba1b4b4e016cc7b1f4b05a6df885a6af39c2bc3b94c02291c84
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2, raw-body@npm:^2.4.1":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 863b5171e140546a4d99f349b720abac4410338e23df5e409cfcc3752538c9caf947ce382c89129ba976f71894bd38b5806c774edac35ebf168d02aa1ac11a95
  languageName: node
  linkType: hard

"rc@npm:1.2.8":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: "npm:^0.6.0"
    ini: "npm:~1.3.0"
    minimist: "npm:^1.2.0"
    strip-json-comments: "npm:~2.0.1"
  bin:
    rc: ./cli.js
  checksum: 5c4d72ae7eec44357171585938c85ce066da8ca79146b5635baf3d55d74584c92575fa4e2c9eac03efbed3b46a0b2e7c30634c012b4b4fa40d654353d3c163eb
  languageName: node
  linkType: hard

"readable-stream@npm:^1.0.33":
  version: 1.1.14
  resolution: "readable-stream@npm:1.1.14"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.1"
    isarray: "npm:0.0.1"
    string_decoder: "npm:~0.10.x"
  checksum: 1aa2cf4bd02f9ab3e1d57842a43a413b52be5300aa089ad1f2e3cea00684532d73edc6a2ba52b0c3210d8b57eb20a695a6d2b96d1c6085ee979c6021ad48ad20
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.0, readable-stream@npm:^2.2.9":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 8500dd3a90e391d6c5d889256d50ec6026c059fadee98ae9aa9b86757d60ac46fff24fafb7a39fa41d54cb39d8be56cc77be202ebd4cd8ffcf4cb226cbaa40d4
  languageName: node
  linkType: hard

"readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: d9e3e53193adcdb79d8f10f2a1f6989bd4389f5936c6f8b870e77570853561c362bee69feca2bbb7b32368ce96a85504aa4cedf7cf80f36e6a9de30d64244048
  languageName: node
  linkType: hard

"readable-stream@npm:~1.0.15":
  version: 1.0.34
  resolution: "readable-stream@npm:1.0.34"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.1"
    isarray: "npm:0.0.1"
    string_decoder: "npm:~0.10.x"
  checksum: 20537fca5a8ffd4af0f483be1cce0e981ed8cbb1087e0c762e2e92ae77f1005627272cebed8422f28047b465056aa1961fefd24baf532ca6a3616afea6811ae0
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 196b30ef6ccf9b6e18c4e1724b7334f72a093d011a99f3b5920470f0b3406a51770867b3e1ae9711f227ef7a7065982f6ee2ce316746b2cb42c88efe44297fe7
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.0
  resolution: "regenerator-runtime@npm:0.14.0"
  checksum: 6c19495baefcf5fbb18a281b56a97f0197b5f219f42e571e80877f095320afac0bdb31dab8f8186858e6126950068c3f17a1226437881e3e70446ea66751897c
  languageName: node
  linkType: hard

"registry-auth-token@npm:^5.0.1":
  version: 5.0.2
  resolution: "registry-auth-token@npm:5.0.2"
  dependencies:
    "@pnpm/npm-conf": "npm:^2.1.0"
  checksum: 0d7683b71ee418993e7872b389024b13645c4295eb7bb850d10728eaf46065db24ea4d47dc6cbb71a60d1aa4bef077b0d8b7363c9ac9d355fdba47bebdfb01dd
  languageName: node
  linkType: hard

"registry-url@npm:^6.0.0":
  version: 6.0.1
  resolution: "registry-url@npm:6.0.1"
  dependencies:
    rc: "npm:1.2.8"
  checksum: 33712aa1b489aab7aba2191c1cdadfdd71f5bf166d4792d81744a6be332c160bd7d9273af8269d8a01284b9562f14a5b31b7abcf7ad9306c44887ecff51c89ab
  languageName: node
  linkType: hard

"request@npm:^2.79.0, request@npm:^2.85.0":
  version: 2.88.2
  resolution: "request@npm:2.88.2"
  dependencies:
    aws-sign2: "npm:~0.7.0"
    aws4: "npm:^1.8.0"
    caseless: "npm:~0.12.0"
    combined-stream: "npm:~1.0.6"
    extend: "npm:~3.0.2"
    forever-agent: "npm:~0.6.1"
    form-data: "npm:~2.3.2"
    har-validator: "npm:~5.1.3"
    http-signature: "npm:~1.2.0"
    is-typedarray: "npm:~1.0.0"
    isstream: "npm:~0.1.2"
    json-stringify-safe: "npm:~5.0.1"
    mime-types: "npm:~2.1.19"
    oauth-sign: "npm:~0.9.0"
    performance-now: "npm:^2.1.0"
    qs: "npm:~6.5.2"
    safe-buffer: "npm:^5.1.2"
    tough-cookie: "npm:~2.5.0"
    tunnel-agent: "npm:^0.6.0"
    uuid: "npm:^3.3.2"
  checksum: 005b8b237b56f1571cfd4ecc09772adaa2e82dcb884fc14ea2bb25e23dbf7c2009f9929e0b6d3fd5802e33ed8ee705a3b594c8f9467c1458cd973872bf89db8e
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: a72468e2589270d91f06c7d36ec97a88db53ae5d6fe3787fadc943f0b0276b10347f89b363b2a82285f650bdcc135ad4a257c61bdd4d00d6df1fa24875b0ddaf
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.0, require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 839a3a890102a658f4cb3e7b2aa13a1f80a3a976b512020c3d1efc418491c48a886b6e481ea56afc6c4cb5eef678f23b2a4e70575e7534eccadf5e30ed2e56eb
  languageName: node
  linkType: hard

"resolve-alpn@npm:^1.0.0, resolve-alpn@npm:^1.2.0":
  version: 1.2.1
  resolution: "resolve-alpn@npm:1.2.1"
  checksum: 744e87888f0b6fa0b256ab454ca0b9c0b80808715e2ef1f3672773665c92a941f6181194e30ccae4a8cd0adbe0d955d3f133102636d2ee0cca0119fec0bc9aec
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"resolve@npm:1.17.0":
  version: 1.17.0
  resolution: "resolve@npm:1.17.0"
  dependencies:
    path-parse: "npm:^1.0.6"
  checksum: 74141da8c56192fd46f6aa887864f8fd74c1755425174526610cb775177278bb414c6f6feb3051ccd73d774d2ae124c6c97e463e30d7ffd9a87f7da202b851dd
  languageName: node
  linkType: hard

"resolve@npm:^1.14.2":
  version: 1.22.8
  resolution: "resolve@npm:1.22.8"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: c473506ee01eb45cbcfefb68652ae5759e092e6b0fb64547feadf9736a6394f258fbc6f88e00c5ca36d5477fbb65388b272432a3600fa223062e54333c156753
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A1.17.0#optional!builtin<compat/resolve>":
  version: 1.17.0
  resolution: "resolve@patch:resolve@npm%3A1.17.0#optional!builtin<compat/resolve>::version=1.17.0&hash=c3c19d"
  dependencies:
    path-parse: "npm:^1.0.6"
  checksum: 02e87fe9233d169fdc5220572c7b8933c9e23323aaecfd5b8d0b106a7f09dc676dd4d380e66c72b1369489292bcb337b13aad28b480a1bde5a5c040ff16758ea
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>":
  version: 1.22.8
  resolution: "resolve@patch:resolve@npm%3A1.22.8#optional!builtin<compat/resolve>::version=1.22.8&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: f345cd37f56a2c0275e3fe062517c650bb673815d885e7507566df589375d165bbbf4bdb6aa95600a9bc55f4744b81f452b5a63f95b9f10a72787dba3c90890a
  languageName: node
  linkType: hard

"responselike@npm:^2.0.0":
  version: 2.0.1
  resolution: "responselike@npm:2.0.1"
  dependencies:
    lowercase-keys: "npm:^2.0.0"
  checksum: b122535466e9c97b55e69c7f18e2be0ce3823c5d47ee8de0d9c0b114aa55741c6db8bfbfce3766a94d1272e61bfb1ebf0a15e9310ac5629fbb7446a861b4fd3a
  languageName: node
  linkType: hard

"responselike@npm:^3.0.0":
  version: 3.0.0
  resolution: "responselike@npm:3.0.0"
  dependencies:
    lowercase-keys: "npm:^3.0.0"
  checksum: e0cc9be30df4f415d6d83cdede3c5c887cd4a73e7cc1708bcaab1d50a28d15acb68460ac5b02bcc55a42f3d493729c8856427dcf6e57e6e128ad05cba4cfb95e
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 14222c9e1d3f9ae01480c50d96057228a8524706db79cdeb5a2ce5bb7070dd9f409a6f84a02cbef8cdc80d39aef86f2dd03d155188a1300c599b05437dcd2ffb
  languageName: node
  linkType: hard

"rimraf@npm:^2.2.8":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 4586c296c736483e297da7cffd19475e4a3e41d07b1ae124aad5d687c79e4ffa716bdac8732ed1db942caf65271cee9dd39f8b639611de161a2753e2112ffe1d
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.5
  resolution: "rimraf@npm:5.0.5"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: a612c7184f96258b7d1328c486b12ca7b60aa30e04229a08bbfa7e964486deb1e9a1b52d917809311bdc39a808a4055c0f950c0280fba194ba0a09e6f0d404f6
  languageName: node
  linkType: hard

"ripemd160@npm:^2.0.0, ripemd160@npm:^2.0.1":
  version: 2.0.2
  resolution: "ripemd160@npm:2.0.2"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
  checksum: 006accc40578ee2beae382757c4ce2908a826b27e2b079efdcd2959ee544ddf210b7b5d7d5e80467807604244e7388427330f5c6d4cd61e6edaddc5773ccc393
  languageName: node
  linkType: hard

"rlp@npm:^2.0.0, rlp@npm:^2.2.3, rlp@npm:^2.2.4":
  version: 2.2.7
  resolution: "rlp@npm:2.2.7"
  dependencies:
    bn.js: "npm:^5.2.0"
  bin:
    rlp: bin/rlp
  checksum: cf1919a2dc99f336191b3363b76299db567c192b7ee3c6f5c722728c34f65577883c9c88eeb7a1bfcbc26693c8a4f1fb0662e79ee86f0c98dd258d6987303498
  languageName: node
  linkType: hard

"run-applescript@npm:^5.0.0":
  version: 5.0.0
  resolution: "run-applescript@npm:5.0.0"
  dependencies:
    execa: "npm:^5.0.0"
  checksum: d00c2dbfa5b2d774de7451194b8b125f40f65fc183de7d9dcae97f57f59433586d3c39b9001e111c38bfa24c3436c99df1bb4066a2a0c90d39a8c4cd6889af77
  languageName: node
  linkType: hard

"run-parallel-limit@npm:^1.1.0":
  version: 1.1.0
  resolution: "run-parallel-limit@npm:1.1.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 672c3b87e7f939c684b9965222b361421db0930223ed1e43ebf0e7e48ccc1a022ea4de080bef4d5468434e2577c33b7681e3f03b7593fdc49ad250a55381123c
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rustbn.js@npm:~0.2.0":
  version: 0.2.0
  resolution: "rustbn.js@npm:0.2.0"
  checksum: 2d7d09f6bea2b5fb05142724f5cfc65c8d96b6e57a29874060733d041789aabbd236617c05d8569a43a2997eea850b4323527e92368c46d04a671ef0b2319fe9
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:^5.2.0, safe-buffer@npm:^5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 7eb5b48f2ed9a594a4795677d5a150faa7eb54483b2318b568dc0c4fc94092a6cce5be02c7288a0500a156282f5276d5688bce7259299568d1053b2150ef374a
  languageName: node
  linkType: hard

"safe-event-emitter@npm:^1.0.1":
  version: 1.0.1
  resolution: "safe-event-emitter@npm:1.0.1"
  dependencies:
    events: "npm:^3.0.0"
  checksum: 2a15094bd28b0966571693f219b5a846949ae24f7ba87c6024f0ed552bef63ebe72970a784b85b77b1f03f1c95e78fabe19306d44538dbc4a3a685bed31c18c4
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0, safer-buffer@npm:^2.0.2, safer-buffer@npm:^2.1.0, safer-buffer@npm:~2.1.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"scrypt-js@npm:3.0.1, scrypt-js@npm:^3.0.0, scrypt-js@npm:^3.0.1":
  version: 3.0.1
  resolution: "scrypt-js@npm:3.0.1"
  checksum: 2f8aa72b7f76a6f9c446bbec5670f80d47497bccce98474203d89b5667717223eeb04a50492ae685ed7adc5a060fc2d8f9fd988f8f7ebdaf3341967f3aeff116
  languageName: node
  linkType: hard

"secp256k1@npm:4.0.3, secp256k1@npm:^4.0.1":
  version: 4.0.3
  resolution: "secp256k1@npm:4.0.3"
  dependencies:
    elliptic: "npm:^6.5.4"
    node-addon-api: "npm:^2.0.0"
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.2.0"
  checksum: 8b45820cd90fd2f95cc8fdb9bf8a71e572de09f2311911ae461a951ffa9e30c99186a129d0f1afeb380dd67eca0c10493f8a7513c39063fda015e99995088e3b
  languageName: node
  linkType: hard

"semaphore@npm:>=1.0.1, semaphore@npm:^1.0.3":
  version: 1.1.0
  resolution: "semaphore@npm:1.1.0"
  checksum: 7a62e0486ab2e361aecf5d88ecb99f42236bb9fbe29e432644d8af371e78c559dc3f2fbef0f57f716491071c214bdf1a15f1f398445b20423278750e2290ea37
  languageName: node
  linkType: hard

"semver@npm:^5.5.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fca14418a174d4b4ef1fecb32c5941e3412d52a4d3d85165924ce3a47fbc7073372c26faf7484ceb4bbc2bde25880c6b97e492473dc7e9708fdfb1c6a02d546e
  languageName: node
  linkType: hard

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 1ef3a85bd02a760c6ef76a45b8c1ce18226de40831e02a00bad78485390b98b6ccaa31046245fc63bba4a47a6a592b6c7eedc65cc47126e60489f9cc1ce3ed7e
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.5.2, semver@npm:^7.5.4":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: "npm:^6.0.0"
  bin:
    semver: bin/semver.js
  checksum: 985dec0d372370229a262c737063860fabd4a1c730662c1ea3200a2f649117761a42184c96df62a0e885e76fbd5dace41087d6c1ac0351b13c0df5d6bcb1b5ac
  languageName: node
  linkType: hard

"semver@npm:~5.4.1":
  version: 5.4.1
  resolution: "semver@npm:5.4.1"
  bin:
    semver: ./bin/semver
  checksum: b5178aac04ab3595e0a22f2e9d5d4388353919d952b1790318767152f8605487ed1ec488d88dcf927ed9e4f9ed8d7a0018523ba00bcf1a253601eaab7c20df67
  languageName: node
  linkType: hard

"send@npm:0.18.0":
  version: 0.18.0
  resolution: "send@npm:0.18.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: ec66c0ad109680ad8141d507677cfd8b4e40b9559de23191871803ed241718e99026faa46c398dcfb9250676076573bd6bfe5d0ec347f88f4b7b8533d1d391cb
  languageName: node
  linkType: hard

"serialize-javascript@npm:6.0.0":
  version: 6.0.0
  resolution: "serialize-javascript@npm:6.0.0"
  dependencies:
    randombytes: "npm:^2.1.0"
  checksum: ed3dabfbb565c48c9eb1ca8fe58f0d256902ab70a8a605be634ddd68388d5f728bb0bd1268e94fab628748ba8ad8392f01b05f3cbe1e4878b5c58c669fd3d1b4
  languageName: node
  linkType: hard

"serve-static@npm:1.15.0":
  version: 1.15.0
  resolution: "serve-static@npm:1.15.0"
  dependencies:
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.18.0"
  checksum: 699b2d4c29807a51d9b5e0f24955346911437aebb0178b3c4833ad30d3eca93385ff9927254f5c16da345903cad39d9cd4a532198c95a5129cc4ed43911b15a4
  languageName: node
  linkType: hard

"servify@npm:^0.1.12":
  version: 0.1.12
  resolution: "servify@npm:0.1.12"
  dependencies:
    body-parser: "npm:^1.16.0"
    cors: "npm:^2.8.1"
    express: "npm:^4.14.0"
    request: "npm:^2.79.0"
    xhr: "npm:^2.3.3"
  checksum: d61b145034aa26c143d7081a56c544aceff256eead27a5894b6785346254438d2b387ac7411bf664024d258779a00dc6c5d9da65f8d60382dac23a8cba0b0d9e
  languageName: node
  linkType: hard

"set-function-length@npm:^1.1.1":
  version: 1.1.1
  resolution: "set-function-length@npm:1.1.1"
  dependencies:
    define-data-property: "npm:^1.1.1"
    get-intrinsic: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
  checksum: 745ed1d7dc69a6185e0820082fe73838ab3dfd01e75cce83a41e4c1d68bbf34bc5fb38f32ded542ae0b557536b5d2781594499b5dcd19e7db138e06292a76c7b
  languageName: node
  linkType: hard

"set-immediate-shim@npm:^1.0.1":
  version: 1.0.1
  resolution: "set-immediate-shim@npm:1.0.1"
  checksum: 98c4d6778c98363436690a340077142ef11c1a8c8c6a78118242340c8e82bb2d66a1563707ef3f5455e7ff5cbee21c87e898b333cf3e7ed241bab12d19e9eab1
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 76e3f5d7f4b581b6100ff819761f04a984fa3f3990e72a6554b57188ded53efce2d3d6c0932c10f810b7c59414f85e2ab3c11521877d1dea1ce0b56dc906f485
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: fde1630422502fbbc19e6844346778f99d449986b2f9cdcceb8326730d2f3d9964dbcb03c02aaadaefffecd0f2c063315ebea8b3ad895914bf1afc1747fc172e
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.0, sha.js@npm:^2.4.8":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  bin:
    sha.js: ./bin.js
  checksum: d833bfa3e0a67579a6ce6e1bc95571f05246e0a441dd8c76e3057972f2a3e098465687a4369b07e83a0375a88703577f71b5b2e966809e67ebc340dbedb478c7
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: "npm:^1.0.0"
    get-intrinsic: "npm:^1.0.2"
    object-inspect: "npm:^1.9.0"
  checksum: c4998d9fc530b0e75a7fd791ad868fdc42846f072734f9080ff55cc8dc7d3899abcda24fd896aa6648c3ab7021b4bb478073eb4f44dfd55bce9714bc1a7c5d45
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"simple-concat@npm:^1.0.0":
  version: 1.0.1
  resolution: "simple-concat@npm:1.0.1"
  checksum: 4d211042cc3d73a718c21ac6c4e7d7a0363e184be6a5ad25c8a1502e49df6d0a0253979e3d50dbdd3f60ef6c6c58d756b5d66ac1e05cda9cacd2e9fc59e3876a
  languageName: node
  linkType: hard

"simple-get@npm:^2.7.0":
  version: 2.8.2
  resolution: "simple-get@npm:2.8.2"
  dependencies:
    decompress-response: "npm:^3.3.0"
    once: "npm:^1.3.1"
    simple-concat: "npm:^1.0.0"
  checksum: b827672695bbe504217311c47c6a106358babcfbf3d69c8d67ad56da40c2ed05185eec12538dfe3637e1cf0441bcd5931b022a84dc7f8f2d84969d595f7f7fda
  languageName: node
  linkType: hard

"slash@npm:^4.0.0":
  version: 4.0.0
  resolution: "slash@npm:4.0.0"
  checksum: da8e4af73712253acd21b7853b7e0dbba776b786e82b010a5bfc8b5051a1db38ed8aba8e1e8f400dd2c9f373be91eb1c42b66e91abb407ff42b10feece5e1d2d
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    astral-regex: "npm:^2.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
  checksum: 4a82d7f085b0e1b070e004941ada3c40d3818563ac44766cca4ceadd2080427d337554f9f99a13aaeb3b4a94d9964d9466c807b3d7b7541d1ec37ee32d308756
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.1":
  version: 8.0.2
  resolution: "socks-proxy-agent@npm:8.0.2"
  dependencies:
    agent-base: "npm:^7.0.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.7.1"
  checksum: ea727734bd5b2567597aa0eda14149b3b9674bb44df5937bbb9815280c1586994de734d965e61f1dd45661183d7b41f115fb9e432d631287c9063864cfcc2ecc
  languageName: node
  linkType: hard

"socks@npm:^2.7.1":
  version: 2.7.1
  resolution: "socks@npm:2.7.1"
  dependencies:
    ip: "npm:^2.0.0"
    smart-buffer: "npm:^4.2.0"
  checksum: 5074f7d6a13b3155fa655191df1c7e7a48ce3234b8ccf99afa2ccb56591c195e75e8bb78486f8e9ea8168e95a29573cbaad55b2b5e195160ae4d2ea6811ba833
  languageName: node
  linkType: hard

"solc@npm:0.7.3":
  version: 0.7.3
  resolution: "solc@npm:0.7.3"
  dependencies:
    command-exists: "npm:^1.2.8"
    commander: "npm:3.0.2"
    follow-redirects: "npm:^1.12.1"
    fs-extra: "npm:^0.30.0"
    js-sha3: "npm:0.8.0"
    memorystream: "npm:^0.3.1"
    require-from-string: "npm:^2.0.0"
    semver: "npm:^5.5.0"
    tmp: "npm:0.0.33"
  bin:
    solcjs: solcjs
  checksum: 68bb783765d1aacf6ebe151ddbffff4c17f679046f2f83a2abae99c57cc0e7dbbcebd62b31861892df18fde272697c37c7a7518f1a9b1219de80217f0c780f0b
  languageName: node
  linkType: hard

"solhint@npm:^4.0.0":
  version: 4.0.0
  resolution: "solhint@npm:4.0.0"
  dependencies:
    "@solidity-parser/parser": "npm:^0.16.0"
    ajv: "npm:^6.12.6"
    antlr4: "npm:^4.11.0"
    ast-parents: "npm:^0.0.1"
    chalk: "npm:^4.1.2"
    commander: "npm:^10.0.0"
    cosmiconfig: "npm:^8.0.0"
    fast-diff: "npm:^1.2.0"
    glob: "npm:^8.0.3"
    ignore: "npm:^5.2.4"
    js-yaml: "npm:^4.1.0"
    latest-version: "npm:^7.0.0"
    lodash: "npm:^4.17.21"
    pluralize: "npm:^8.0.0"
    prettier: "npm:^2.8.3"
    semver: "npm:^7.5.2"
    strip-ansi: "npm:^6.0.1"
    table: "npm:^6.8.1"
    text-table: "npm:^0.2.0"
  dependenciesMeta:
    prettier:
      optional: true
  bin:
    solhint: solhint.js
  checksum: 41e01c292ea16d9b1303d689c6381426aa845697911edd915ccf4019b58fae499af4ee7c47aa2c83601535f3824a1d27220735aa8e35816e03768518ccb18fb6
  languageName: node
  linkType: hard

"solidity-bytes-utils@npm:^0.8.0":
  version: 0.8.1
  resolution: "solidity-bytes-utils@npm:0.8.1"
  dependencies:
    "@truffle/hdwallet-provider": "npm:latest"
  checksum: 6876fcb77e09b9b1168ffb19eaac2afdf07b00c0562b9dffed423023b99c6d7a03d7df984090e10a5d6dcb48fbf55065c89fe56dc7da16586f2a004301c563bc
  languageName: node
  linkType: hard

"solidity-comments-extractor@npm:^0.0.7":
  version: 0.0.7
  resolution: "solidity-comments-extractor@npm:0.0.7"
  checksum: a5cedf2310709969bc1783a6c336171478536f2f0ea96ad88437e0ef1e8844c0b37dd75591b0a824ec9c30640ea7e31b5f03128e871e6235bef3426617ce96c4
  languageName: node
  linkType: hard

"sort-object-keys@npm:^1.1.3":
  version: 1.1.3
  resolution: "sort-object-keys@npm:1.1.3"
  checksum: abea944d6722a1710a1aa6e4f9509da085d93d5fc0db23947cb411eedc7731f80022ce8fa68ed83a53dd2ac7441fcf72a3f38c09b3d9bbc4ff80546aa2e151ad
  languageName: node
  linkType: hard

"sort-package-json@npm:2.6.0":
  version: 2.6.0
  resolution: "sort-package-json@npm:2.6.0"
  dependencies:
    detect-indent: "npm:^7.0.1"
    detect-newline: "npm:^4.0.0"
    get-stdin: "npm:^9.0.0"
    git-hooks-list: "npm:^3.0.0"
    globby: "npm:^13.1.2"
    is-plain-obj: "npm:^4.1.0"
    sort-object-keys: "npm:^1.1.3"
  bin:
    sort-package-json: cli.js
  checksum: d955b1701757934e79bf6b0db7348106e8ab2d4972878210cbabae501b7d6d81d548f9b4ef1f357201914da9b643a60b87c77eaea171672728f3a9e5c83e00df
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.13, source-map-support@npm:^0.5.16":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 8317e12d84019b31e34b86d483dd41d6f832f389f7417faf8fc5c75a66a12d9686e47f589a0554a868b8482f037e23df9d040d29387eb16fa14cb85f091ba207
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ef7462f1c29d502b3057e822cdbdae0b0e565302c4dd1a95e11e793d8d9d62006cdc10e0fd99163ca33ff2071360cf50ee13f90440806e7ed57d81cba2f7ff
  languageName: node
  linkType: hard

"sshpk@npm:^1.7.0":
  version: 1.18.0
  resolution: "sshpk@npm:1.18.0"
  dependencies:
    asn1: "npm:~0.2.3"
    assert-plus: "npm:^1.0.0"
    bcrypt-pbkdf: "npm:^1.0.0"
    dashdash: "npm:^1.12.0"
    ecc-jsbn: "npm:~0.1.1"
    getpass: "npm:^0.1.1"
    jsbn: "npm:~0.1.0"
    safer-buffer: "npm:^2.0.2"
    tweetnacl: "npm:~0.14.0"
  bin:
    sshpk-conv: bin/sshpk-conv
    sshpk-sign: bin/sshpk-sign
    sshpk-verify: bin/sshpk-verify
  checksum: 858339d43e3c6b6a848772a66f69442ce74f1a37655d9f35ba9d1f85329499ff0000af9f8ab83dbb39ad24c0c370edabe0be1e39863f70c6cded9924b8458c34
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.5
  resolution: "ssri@npm:10.0.5"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 453f9a1c241c13f5dfceca2ab7b4687bcff354c3ccbc932f35452687b9ef0ccf8983fd13b8a3baa5844c1a4882d6e3ddff48b0e7fd21d743809ef33b80616d79
  languageName: node
  linkType: hard

"stacktrace-parser@npm:^0.1.10":
  version: 0.1.10
  resolution: "stacktrace-parser@npm:0.1.10"
  dependencies:
    type-fest: "npm:^0.7.1"
  checksum: f4fbddfc09121d91e587b60de4beb4941108e967d71ad3a171812dc839b010ca374d064ad0a296295fed13acd103609d99a4224a25b4e67de13cae131f1901ee
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^1.0.0":
  version: 1.1.0
  resolution: "strict-uri-encode@npm:1.1.0"
  checksum: 9466d371f7b36768d43f7803f26137657559e4c8b0161fb9e320efb8edba3ae22f8e99d4b0d91da023b05a13f62ec5412c3f4f764b5788fac11d1fea93720bb3
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 54d23f4a6acae0e93f999a585e673be9e561b65cd4cca37714af1e893ab8cd8dfa52a9e4f58f48f87b4a44918d3a9254326cb80ed194bf2e4c226e2b21767e56
  languageName: node
  linkType: hard

"string_decoder@npm:~0.10.x":
  version: 0.10.31
  resolution: "string_decoder@npm:0.10.31"
  checksum: cc43e6b1340d4c7843da0e37d4c87a4084c2342fc99dcf6563c3ec273bb082f0cbd4ebf25d5da19b04fb16400d393885fda830be5128e1c416c73b5a6165f175
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 7c41c17ed4dea105231f6df208002ebddd732e8e9e2d619d133cecd8e0087ddfd9587d2feb3c8caf3213cbd841ada6d057f5142cae68a4e62d3540778d9819b4
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-final-newline@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-final-newline@npm:3.0.0"
  checksum: 23ee263adfa2070cd0f23d1ac14e2ed2f000c9b44229aec9c799f1367ec001478469560abefd00c5c99ee6f0b31c137d53ec6029c53e9f32a93804e18c201050
  languageName: node
  linkType: hard

"strip-hex-prefix@npm:1.0.0":
  version: 1.0.0
  resolution: "strip-hex-prefix@npm:1.0.0"
  dependencies:
    is-hex-prefixed: "npm:1.0.0"
  checksum: 4cafe7caee1d281d3694d14920fd5d3c11adf09371cef7e2ccedd5b83efd9e9bd2219b5d6ce6e809df6e0f437dc9d30db1192116580875698aad164a6d6b285b
  languageName: node
  linkType: hard

"strip-json-comments@npm:3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"supports-color@npm:8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 157b534df88e39c5518c5e78c35580c1eca848d7dbaf31bbe06cdfc048e22c7ff1a9d046ae17b25691128f631a51d9ec373c1b740c12ae4f0de6e292037e4282
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 5f505c6fa3c6e05873b43af096ddeb22159831597649881aeb8572d6fe3b81e798cc10840d0c9735e0026b250368851b7f77b65e84f4e4daa820a4f69947f55b
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: a9dc19ae2220c952bd2231d08ddeecb1b0328b61e72071ff4000c8384e145cc07c1c0bdb3b5a1cb06e186a7b2790f1dee793418b332f6ddf320de25d9125be7e
  languageName: node
  linkType: hard

"swarm-js@npm:^0.1.40":
  version: 0.1.42
  resolution: "swarm-js@npm:0.1.42"
  dependencies:
    bluebird: "npm:^3.5.0"
    buffer: "npm:^5.0.5"
    eth-lib: "npm:^0.1.26"
    fs-extra: "npm:^4.0.2"
    got: "npm:^11.8.5"
    mime-types: "npm:^2.1.16"
    mkdirp-promise: "npm:^5.0.1"
    mock-fs: "npm:^4.1.0"
    setimmediate: "npm:^1.0.5"
    tar: "npm:^4.0.2"
    xhr-request: "npm:^1.0.1"
  checksum: 341bcfef6daadc1904ea87b1781f10dc99ec14e33c9a9041e43e9617dcc3b7d632230e1baf2fafecb8e10e63c2e4eeb7cce7c85592dc0cf0dde935f49c77050b
  languageName: node
  linkType: hard

"synckit@npm:0.8.6":
  version: 0.8.6
  resolution: "synckit@npm:0.8.6"
  dependencies:
    "@pkgr/utils": "npm:^2.4.2"
    tslib: "npm:^2.6.2"
  checksum: 565c659b5c935905e3774f8a53b013aeb1db03b69cb26cfea742021a274fba792e6ec22f1f918bfb6a7fe16dc9ab6e32a94b4289a8d5d9039b695cd9d524953d
  languageName: node
  linkType: hard

"table@npm:^6.8.1":
  version: 6.8.1
  resolution: "table@npm:6.8.1"
  dependencies:
    ajv: "npm:^8.0.1"
    lodash.truncate: "npm:^4.4.2"
    slice-ansi: "npm:^4.0.0"
    string-width: "npm:^4.2.3"
    strip-ansi: "npm:^6.0.1"
  checksum: 512c4f2bfb6f46f4d5ced19943ae5db1a5163eac1f23ce752625eb49715f84217c1c62bc2d017eb8985b37e0f85731108f654df809c0b34cca1678a672e7ea20
  languageName: node
  linkType: hard

"tar@npm:^4.0.2":
  version: 4.4.19
  resolution: "tar@npm:4.4.19"
  dependencies:
    chownr: "npm:^1.1.4"
    fs-minipass: "npm:^1.2.7"
    minipass: "npm:^2.9.0"
    minizlib: "npm:^1.3.3"
    mkdirp: "npm:^0.5.5"
    safe-buffer: "npm:^5.2.1"
    yallist: "npm:^3.1.1"
  checksum: 2715b5964578424ba5164632905a85e5a98c8dffeba657860aafa3a771b2602e6fd2a350bca891d78b8bda8cab5c53134c683ed2269b9925533477a24722e73b
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.1.2":
  version: 6.2.0
  resolution: "tar@npm:6.2.0"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 2042bbb14830b5cd0d584007db0eb0a7e933e66d1397e72a4293768d2332449bc3e312c266a0887ec20156dea388d8965e53b4fc5097f42d78593549016da089
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 4383b5baaeffa9bb4cda2ac33a4aa2e6d1f8aaf811848bf73513a9b88fd76372dc461f6fd6d2e9cb5100f48b473be32c6f95bd983509b7d92bb4d92c10747452
  languageName: node
  linkType: hard

"timed-out@npm:^4.0.1":
  version: 4.0.1
  resolution: "timed-out@npm:4.0.1"
  checksum: d52648e5fc0ebb0cae1633737a1db1b7cb464d5d43d754bd120ddebd8067a1b8f42146c250d8cfb9952183b7b0f341a99fc71b59c52d659218afae293165004f
  languageName: node
  linkType: hard

"titleize@npm:^3.0.0":
  version: 3.0.0
  resolution: "titleize@npm:3.0.0"
  checksum: 71fbbeabbfb36ccd840559f67f21e356e1d03da2915b32d2ae1a60ddcc13a124be2739f696d2feb884983441d159a18649e8d956648d591bdad35c430a6b6d28
  languageName: node
  linkType: hard

"tmp@npm:0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: "npm:~1.0.2"
  checksum: 09c0abfd165cff29b32be42bc35e80b8c64727d97dedde6550022e88fa9fd39a084660415ed8e3ebaa2aca1ee142f86df8b31d4196d4f81c774a3a20fd4b6abf
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"tough-cookie@npm:~2.5.0":
  version: 2.5.0
  resolution: "tough-cookie@npm:2.5.0"
  dependencies:
    psl: "npm:^1.1.28"
    punycode: "npm:^2.1.1"
  checksum: 024cb13a4d1fe9af57f4323dff765dd9b217cc2a69be77e3b8a1ca45600aa33a097b6ad949f225d885e904f4bd3ceccef104741ef202d8378e6ca78e850ff82f
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 8f1f5aa6cb232f9e1bdc86f485f916b7aa38caee8a778b378ffec0b70d9307873f253f5cbadbe2955ece2ac5c83d0dc14a77513166ccd0a0c7fe197e21396695
  languageName: node
  linkType: hard

"tslib@npm:^1.9.3":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 7dbf34e6f55c6492637adb81b555af5e3b4f9cc6b998fb440dac82d3b42bdc91560a35a5fb75e20e24a076c651438234da6743d139e4feabf0783f3cdfe1dddb
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.6.0, tslib@npm:^2.6.2":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: bd26c22d36736513980091a1e356378e8b662ded04204453d353a7f34a4c21ed0afc59b5f90719d4ba756e581a162ecbf93118dc9c6be5acf70aa309188166ca
  languageName: node
  linkType: hard

"tsort@npm:0.0.1":
  version: 0.0.1
  resolution: "tsort@npm:0.0.1"
  checksum: 5f15ca0e91142a72d2acb6e9798a0297b754ce402c8f8bbb63457ee17f062272f3ccdf39f4c3155f0568337cb3b5422410b40cfeed72fe75fbb9a71f016cdcf9
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 7f0d9ed5c22404072b2ae8edc45c071772affd2ed14a74f03b4e71b4dd1a14c3714d85aed64abcaaee5fec2efc79002ba81155c708f4df65821b444abb0cfade
  languageName: node
  linkType: hard

"tweetnacl-util@npm:^0.15.1":
  version: 0.15.1
  resolution: "tweetnacl-util@npm:0.15.1"
  checksum: ae6aa8a52cdd21a95103a4cc10657d6a2040b36c7a6da7b9d3ab811c6750a2d5db77e8c36969e75fdee11f511aa2b91c552496c6e8e989b6e490e54aca2864fc
  languageName: node
  linkType: hard

"tweetnacl@npm:^0.14.3, tweetnacl@npm:~0.14.0":
  version: 0.14.5
  resolution: "tweetnacl@npm:0.14.5"
  checksum: 04ee27901cde46c1c0a64b9584e04c96c5fe45b38c0d74930710751ea991408b405747d01dfae72f80fc158137018aea94f9c38c651cb9c318f0861a310c3679
  languageName: node
  linkType: hard

"tweetnacl@npm:^1.0.3":
  version: 1.0.3
  resolution: "tweetnacl@npm:1.0.3"
  checksum: ca122c2f86631f3c0f6d28efb44af2a301d4a557a62a3e2460286b08e97567b258c2212e4ad1cfa22bd6a57edcdc54ba76ebe946847450ab0999e6d48ccae332
  languageName: node
  linkType: hard

"type-detect@npm:^4.0.0, type-detect@npm:^4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 5179e3b8ebc51fce1b13efb75fdea4595484433f9683bbc2dca6d99789dba4e602ab7922d2656f2ce8383987467f7770131d4a7f06a26287db0615d2f4c4ce7d
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: f4254070d9c3d83a6e573bcb95173008d73474ceadbbf620dd32d273940ca18734dff39c2b2480282df9afe5d1675ebed5499a00d791758748ea81f61a38961f
  languageName: node
  linkType: hard

"type-fest@npm:^0.7.1":
  version: 0.7.1
  resolution: "type-fest@npm:0.7.1"
  checksum: 0699b6011bb3f7fac5fd5385e2e09432cde08fa89283f24084f29db00ec69a5445cd3aa976438ec74fc552a9a96f4a04ed390b5cb62eb7483aa4b6e5b935e059
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 0bd9eeae5efd27d98fd63519f999908c009e148039d8e7179a074f105362d4fcc214c38b24f6cda79c87e563cbd12083a4691381ed28559220d4a10c2047bed4
  languageName: node
  linkType: hard

"type@npm:^1.0.1":
  version: 1.2.0
  resolution: "type@npm:1.2.0"
  checksum: b4d4b27d1926028be45fc5baaca205896e2a1fe9e5d24dc892046256efbe88de6acd0149e7353cd24dad596e1483e48ec60b0912aa47ca078d68cdd198b09885
  languageName: node
  linkType: hard

"type@npm:^2.7.2":
  version: 2.7.2
  resolution: "type@npm:2.7.2"
  checksum: 602f1b369fba60687fa4d0af6fcfb814075bcaf9ed3a87637fb384d9ff849e2ad15bc244a431f341374562e51a76c159527ffdb1f1f24b0f1f988f35a301c41d
  languageName: node
  linkType: hard

"typedarray-to-buffer@npm:^3.1.5":
  version: 3.1.5
  resolution: "typedarray-to-buffer@npm:3.1.5"
  dependencies:
    is-typedarray: "npm:^1.0.0"
  checksum: 7c850c3433fbdf4d04f04edfc751743b8f577828b8e1eb93b95a3bce782d156e267d83e20fb32b3b47813e69a69ab5e9b5342653332f7d21c7d1210661a7a72c
  languageName: node
  linkType: hard

"ultron@npm:~1.1.0":
  version: 1.1.1
  resolution: "ultron@npm:1.1.1"
  checksum: 7cc6e8e98a2c62c87ab25a79a274f90492f13f5cf7c622dbda1ec85913e207aed392c26e76ed6250c4f05f842571b05dcce1f8ad0f5ecded64a99002b1fdf6e5
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 0097779d94bc0fd26f0418b3a05472410408877279141ded2bd449167be1aed7ea5b76f756562cb3586a07f251b90799bab22d9019ceba49c037c76445f7cddd
  languageName: node
  linkType: hard

"undici@npm:^5.14.0":
  version: 5.28.2
  resolution: "undici@npm:5.28.2"
  dependencies:
    "@fastify/busboy": "npm:^2.0.0"
  checksum: 07ab8b812c7322f5faba55268562e3626463171ace8a2e5861e5793e69f8901e809bcdeb4013126fe8d1602baac247ea3c0c996073dbf9709516295b0a4dd18f
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: "npm:^4.0.0"
  checksum: 8e2f59b356cb2e54aab14ff98a51ac6c45781d15ceaab6d4f1c2228b780193dc70fae4463ce9e1df4479cb9d3304d7c2043a3fb905bdeca71cc7e8ce27e063df
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 40912a8963fc02fb8b600cf50197df4a275c602c60de4cac4f75879d3c48558cfac48de08a25cc10df8112161f7180b3bbb4d662aadb711568602f9eddee54f0
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 40cdc60f6e61070fe658ca36016a8f4ec216b29bf04a55dce14e3710cc84c7448538ef4dad3728d0bfe29975ccd7bfb5f414c45e7b78883567fb31b246f02dff
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"untildify@npm:^4.0.0":
  version: 4.0.0
  resolution: "untildify@npm:4.0.0"
  checksum: 39ced9c418a74f73f0a56e1ba4634b4d959422dff61f4c72a8e39f60b99380c1b45ed776fbaa0a4101b157e4310d873ad7d114e8534ca02609b4916bb4187fb9
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.13":
  version: 1.0.13
  resolution: "update-browserslist-db@npm:1.0.13"
  dependencies:
    escalade: "npm:^3.1.1"
    picocolors: "npm:^1.0.0"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 9074b4ef34d2ed931f27d390aafdd391ee7c45ad83c508e8fed6aaae1eb68f81999a768ed8525c6f88d4001a4fbf1b8c0268f099d0e8e72088ec5945ac796acf
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"url-set-query@npm:^1.0.0":
  version: 1.0.0
  resolution: "url-set-query@npm:1.0.0"
  checksum: a6e4d1ac5c3e7db8644655a2774b9462d8d95ec7abae341ff53d4a3d03adc2dabc38650dc757659fcbce4859372bbea4a896ac842dd5b54cc22aae087ba35664
  languageName: node
  linkType: hard

"utf-8-validate@npm:^5.0.2":
  version: 5.0.10
  resolution: "utf-8-validate@npm:5.0.10"
  dependencies:
    node-gyp: "npm:latest"
    node-gyp-build: "npm:^4.3.0"
  checksum: b89cbc13b4badad04828349ebb7aa2ab1edcb02b46ab12ce0ba5b2d6886d684ad4e93347819e3c8d36224c8742422d2dca69f5cc16c72ae4d7eeecc0c5cb544b
  languageName: node
  linkType: hard

"utf8@npm:3.0.0":
  version: 3.0.0
  resolution: "utf8@npm:3.0.0"
  checksum: 31d19c4faacbb65b09ebc1c21c32b20bdb0919c6f6773cee5001b99bb83f8e503e7233c08fc71ebb34f7cfebd95cec3243b81d90176097aa2f286cccb4ce866e
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"util@npm:^0.12.5":
  version: 0.12.5
  resolution: "util@npm:0.12.5"
  dependencies:
    inherits: "npm:^2.0.3"
    is-arguments: "npm:^1.0.4"
    is-generator-function: "npm:^1.0.7"
    is-typed-array: "npm:^1.1.3"
    which-typed-array: "npm:^1.1.2"
  checksum: 61a10de7753353dd4d744c917f74cdd7d21b8b46379c1e48e1c4fd8e83f8190e6bd9978fc4e5102ab6a10ebda6019d1b36572fa4a325e175ec8b789a121f6147
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 5d6949693d58cb2e636a84f3ee1c6e7b2f9c16cb1d42d0ecb386d8c025c69e327205aa1c69e2868cc06a01e5e20681fbba55a4e0ed0cce913d60334024eae798
  languageName: node
  linkType: hard

"uuid@npm:^3.3.2":
  version: 3.4.0
  resolution: "uuid@npm:3.4.0"
  bin:
    uuid: ./bin/uuid
  checksum: 4f2b86432b04cc7c73a0dd1bcf11f1fc18349d65d2e4e32dd0fc658909329a1e0cc9244aa93f34c0cccfdd5ae1af60a149251a5f420ec3ac4223a3dab198fb2e
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 9a5f7aa1d6f56dd1e8d5f2478f855f25c645e64e26e347a98e98d95781d5ed20062d6cca2eecb58ba7c84bc3910be95c0451ef4161906abaab44f9cb68ffbdd1
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 9d0b6adb72b736e36f2b1b53da0d559125ba3e39d913b6072f6f033e0c87835b414f0836b45bcfaf2bdf698f92297fea1c3cc19b0b258bc182c9c43cc0fab9f2
  languageName: node
  linkType: hard

"varint@npm:^5.0.0":
  version: 5.0.2
  resolution: "varint@npm:5.0.2"
  checksum: e1a66bf9a6cea96d1f13259170d4d41b845833acf3a9df990ea1e760d279bd70d5b1f4c002a50197efd2168a2fd43eb0b808444600fd4d23651e8d42fe90eb05
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 31389debef15a480849b8331b220782230b9815a8e0dbb7b9a8369559aed2e9a7800cd904d4371ea74f4c3527db456dc8e7ac5befce5f0d289014dbdf47b2242
  languageName: node
  linkType: hard

"verror@npm:1.10.0":
  version: 1.10.0
  resolution: "verror@npm:1.10.0"
  dependencies:
    assert-plus: "npm:^1.0.0"
    core-util-is: "npm:1.0.2"
    extsprintf: "npm:^1.2.0"
  checksum: da548149dd9c130a8a2587c9ee71ea30128d1526925707e2d01ed9c5c45c9e9f86733c66a328247cdd5f7c1516fb25b0f959ba754bfbe15072aa99ff96468a29
  languageName: node
  linkType: hard

"web3-bzz@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-bzz@npm:1.10.0"
  dependencies:
    "@types/node": "npm:^12.12.6"
    got: "npm:12.1.0"
    swarm-js: "npm:^0.1.40"
  checksum: 3cfc6eedc5ba963d6580833e242a2dd6adf6076502cef31f02e3e5a0bb85243b6e1bcf63bb4d194f315ffca5bcf432441c9f2915aa1fea03224c860966b8c628
  languageName: node
  linkType: hard

"web3-core-helpers@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-core-helpers@npm:1.10.0"
  dependencies:
    web3-eth-iban: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: 0af64b9467df7b7d738c28d97ddcdc9d0800838daea25c1197efec5b4e2c50d010b95c1c55ed210e4c5c9a056bb2c1621825623a8d9332ebc93f77c908a0dc80
  languageName: node
  linkType: hard

"web3-core-method@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-core-method@npm:1.10.0"
  dependencies:
    "@ethersproject/transactions": "npm:^5.6.2"
    web3-core-helpers: "npm:1.10.0"
    web3-core-promievent: "npm:1.10.0"
    web3-core-subscriptions: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: a5d9783bd73e0f3e05ec3b7013bcf103303af9f211bed471f791db418b21f12028a5f07509be3d8dabe30f32789dd8529c743363bc4cc90eb16e7a6b8df5c80e
  languageName: node
  linkType: hard

"web3-core-promievent@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-core-promievent@npm:1.10.0"
  dependencies:
    eventemitter3: "npm:4.0.4"
  checksum: 68e9f40f78d92ce1ee9808d04a28a89d20ab4dc36af5ba8405f132044cbb01825f76f35249a9599f9568a95d5e7c9e4a09ada6d4dc2e27e0c1b32c9232c8c973
  languageName: node
  linkType: hard

"web3-core-requestmanager@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-core-requestmanager@npm:1.10.0"
  dependencies:
    util: "npm:^0.12.5"
    web3-core-helpers: "npm:1.10.0"
    web3-providers-http: "npm:1.10.0"
    web3-providers-ipc: "npm:1.10.0"
    web3-providers-ws: "npm:1.10.0"
  checksum: 6be2ef8996987d8781d8b2f4d6dca3312acb3359de67d864733392b8220965466a220a8170dad8d87025d708822f7713bfb0c775f27c889c4f8e3a1974b5076b
  languageName: node
  linkType: hard

"web3-core-subscriptions@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-core-subscriptions@npm:1.10.0"
  dependencies:
    eventemitter3: "npm:4.0.4"
    web3-core-helpers: "npm:1.10.0"
  checksum: 009b85acfa19154bc73f48a55ac80c527cd2815556ab70f8fffc86e007e82b8b702e8ad6512c99fa209e69a7e76fcda5fd326132a553cea3991e1f09225b25a0
  languageName: node
  linkType: hard

"web3-core@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-core@npm:1.10.0"
  dependencies:
    "@types/bn.js": "npm:^5.1.1"
    "@types/node": "npm:^12.12.6"
    bignumber.js: "npm:^9.0.0"
    web3-core-helpers: "npm:1.10.0"
    web3-core-method: "npm:1.10.0"
    web3-core-requestmanager: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: dcef60d5c51a1a6c5f1d8b82c22a1358b6d751d117362792f468cd0918814ede2614d972fb7b10e39bb65b9e9d8ed7a54996cd8f1db84ddf04cf328ce0259d76
  languageName: node
  linkType: hard

"web3-eth-abi@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-eth-abi@npm:1.10.0"
  dependencies:
    "@ethersproject/abi": "npm:^5.6.3"
    web3-utils: "npm:1.10.0"
  checksum: f0839e797d0ad1271ed94650b9850df7630110b7ebc68028f7d7d18906160d90155f56dddaa6c5571b63cafd71ce9b0f84cf35d9e1b50f85c3c93a2469260a98
  languageName: node
  linkType: hard

"web3-eth-accounts@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-eth-accounts@npm:1.10.0"
  dependencies:
    "@ethereumjs/common": "npm:2.5.0"
    "@ethereumjs/tx": "npm:3.3.2"
    eth-lib: "npm:0.2.8"
    ethereumjs-util: "npm:^7.1.5"
    scrypt-js: "npm:^3.0.1"
    uuid: "npm:^9.0.0"
    web3-core: "npm:1.10.0"
    web3-core-helpers: "npm:1.10.0"
    web3-core-method: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: f97f81292f950f2f98ff01136f49b65f4f2468551cfce87169b4820b4f92afec5c3d5a1223c6aba6a46d872c854f56a3911fe36f7f902c0ac1fe43c17125802d
  languageName: node
  linkType: hard

"web3-eth-contract@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-eth-contract@npm:1.10.0"
  dependencies:
    "@types/bn.js": "npm:^5.1.1"
    web3-core: "npm:1.10.0"
    web3-core-helpers: "npm:1.10.0"
    web3-core-method: "npm:1.10.0"
    web3-core-promievent: "npm:1.10.0"
    web3-core-subscriptions: "npm:1.10.0"
    web3-eth-abi: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: eadb91ec20875567732a670b6dcc74fb8192c3b0cdd1bb3055133c74444ec46e4dbfedf3533afbb67996bad23b558b90fbd95c7bb79203f33f35e9369b78cb4d
  languageName: node
  linkType: hard

"web3-eth-ens@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-eth-ens@npm:1.10.0"
  dependencies:
    content-hash: "npm:^2.5.2"
    eth-ens-namehash: "npm:2.0.8"
    web3-core: "npm:1.10.0"
    web3-core-helpers: "npm:1.10.0"
    web3-core-promievent: "npm:1.10.0"
    web3-eth-abi: "npm:1.10.0"
    web3-eth-contract: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: 56a53f1e330caecbcafd7c2fbad8a3fb84354c40df86236d7e8953a532e7b970cb6ac70ef995a1c6d9f0af227f902cb90a066d933db20d95b565e000bf7f6b4f
  languageName: node
  linkType: hard

"web3-eth-iban@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-eth-iban@npm:1.10.0"
  dependencies:
    bn.js: "npm:^5.2.1"
    web3-utils: "npm:1.10.0"
  checksum: 02e0a1b071b7cda1c03d2940d93f585724b8348d5d65a949f174f8bd48aeff8d6cfc700123150bf39265122667c46e2542e671c54e4bacd53f9895d31109a4c7
  languageName: node
  linkType: hard

"web3-eth-personal@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-eth-personal@npm:1.10.0"
  dependencies:
    "@types/node": "npm:^12.12.6"
    web3-core: "npm:1.10.0"
    web3-core-helpers: "npm:1.10.0"
    web3-core-method: "npm:1.10.0"
    web3-net: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: abf9f2fbc4f7723be01f778a60511eb3303172f60f66f7d9cf0ce5bca6f0278ec27e3094bb2ef043f2f4025e44ccd44dba793b3e26294dfb2f38cfee27888daa
  languageName: node
  linkType: hard

"web3-eth@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-eth@npm:1.10.0"
  dependencies:
    web3-core: "npm:1.10.0"
    web3-core-helpers: "npm:1.10.0"
    web3-core-method: "npm:1.10.0"
    web3-core-subscriptions: "npm:1.10.0"
    web3-eth-abi: "npm:1.10.0"
    web3-eth-accounts: "npm:1.10.0"
    web3-eth-contract: "npm:1.10.0"
    web3-eth-ens: "npm:1.10.0"
    web3-eth-iban: "npm:1.10.0"
    web3-eth-personal: "npm:1.10.0"
    web3-net: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: 9e254707af47ea9d701b9decd0576c76c6f27b5122da3a1570e6674b9ba4a9a5ad9f52562c03b312a24c73d0478f6de548823ce24e8013293d3d3a7f7a88596a
  languageName: node
  linkType: hard

"web3-net@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-net@npm:1.10.0"
  dependencies:
    web3-core: "npm:1.10.0"
    web3-core-method: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: 5183d897ccf539adafa60e8372871f8d8ecf4c46a0943aeee1d5f78a54c8faddfcb2406269ab422e57ef871c29496dba1bffbe044693b559a3bcd7957af87363
  languageName: node
  linkType: hard

"web3-provider-engine@npm:16.0.3":
  version: 16.0.3
  resolution: "web3-provider-engine@npm:16.0.3"
  dependencies:
    "@ethereumjs/tx": "npm:^3.3.0"
    async: "npm:^2.5.0"
    backoff: "npm:^2.5.0"
    clone: "npm:^2.0.0"
    cross-fetch: "npm:^2.1.0"
    eth-block-tracker: "npm:^4.4.2"
    eth-json-rpc-filters: "npm:^4.2.1"
    eth-json-rpc-infura: "npm:^5.1.0"
    eth-json-rpc-middleware: "npm:^6.0.0"
    eth-rpc-errors: "npm:^3.0.0"
    eth-sig-util: "npm:^1.4.2"
    ethereumjs-block: "npm:^1.2.2"
    ethereumjs-util: "npm:^5.1.5"
    ethereumjs-vm: "npm:^2.3.4"
    json-stable-stringify: "npm:^1.0.1"
    promise-to-callback: "npm:^1.0.0"
    readable-stream: "npm:^2.2.9"
    request: "npm:^2.85.0"
    semaphore: "npm:^1.0.3"
    ws: "npm:^5.1.1"
    xhr: "npm:^2.2.0"
    xtend: "npm:^4.0.1"
  checksum: 6b1e6fec20c674868b32a31a1ba2051cb8e89414ff931f52e0235ee211ffb31c89f7a61a37a851f4fa2db4d2ccae0d3b099e42b5d6e01cdc8f4d2d312b927275
  languageName: node
  linkType: hard

"web3-providers-http@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-providers-http@npm:1.10.0"
  dependencies:
    abortcontroller-polyfill: "npm:^1.7.3"
    cross-fetch: "npm:^3.1.4"
    es6-promise: "npm:^4.2.8"
    web3-core-helpers: "npm:1.10.0"
  checksum: 4a1742056f4f3b548d5ed3bc763470d1c49b00586eeb11ed6c0ff4ce3397b6fec69259dec48b2bb5f8b64f2bf81b275e720e4a488a0d3510aab234273b537ff7
  languageName: node
  linkType: hard

"web3-providers-ipc@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-providers-ipc@npm:1.10.0"
  dependencies:
    oboe: "npm:2.1.5"
    web3-core-helpers: "npm:1.10.0"
  checksum: 7e1f42ceb8fb945589fbb2c85464d26e49df4649fdbbec578b2fa8114484b334f8fa47a585f4ee86efc2c9a3dc3971a0ccc829e3297d00c73ba4a3c291f81963
  languageName: node
  linkType: hard

"web3-providers-ws@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-providers-ws@npm:1.10.0"
  dependencies:
    eventemitter3: "npm:4.0.4"
    web3-core-helpers: "npm:1.10.0"
    websocket: "npm:^1.0.32"
  checksum: 6a0f765b929592c4f45b6286002f56d65bb972c5ecffb5c911442bfd8062df400726b54cd26777a5ab43f57ac861163f55e06b1867c0b45d8a8fea2ebf25aeaa
  languageName: node
  linkType: hard

"web3-shh@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-shh@npm:1.10.0"
  dependencies:
    web3-core: "npm:1.10.0"
    web3-core-method: "npm:1.10.0"
    web3-core-subscriptions: "npm:1.10.0"
    web3-net: "npm:1.10.0"
  checksum: d51b29a2c953855b2ace74c53a18fe24399ff34533750710096d249a164725cd64a686fc90a5b1e4209c85a3824d6ec02c5d4615dd3ca66c020e8d4a18dba1bf
  languageName: node
  linkType: hard

"web3-utils@npm:1.10.0":
  version: 1.10.0
  resolution: "web3-utils@npm:1.10.0"
  dependencies:
    bn.js: "npm:^5.2.1"
    ethereum-bloom-filters: "npm:^1.0.6"
    ethereumjs-util: "npm:^7.1.0"
    ethjs-unit: "npm:0.1.6"
    number-to-bn: "npm:1.7.0"
    randombytes: "npm:^2.1.0"
    utf8: "npm:3.0.0"
  checksum: 8766d5bafafe37a0c647c79ea1adf5782e90b8db71133a92e5e492d04af3be65c33562a22f5a29a303c034d1fa755d08a71bd83e3a3e236101bd0e13e75b31da
  languageName: node
  linkType: hard

"web3@npm:1.10.0":
  version: 1.10.0
  resolution: "web3@npm:1.10.0"
  dependencies:
    web3-bzz: "npm:1.10.0"
    web3-core: "npm:1.10.0"
    web3-eth: "npm:1.10.0"
    web3-eth-personal: "npm:1.10.0"
    web3-net: "npm:1.10.0"
    web3-shh: "npm:1.10.0"
    web3-utils: "npm:1.10.0"
  checksum: 9c8349a46d01bc1b91483a953f6e9078b3a15af451a3092c0ac663cb05f6acb1be6ea4ce82646af27373889bdc9ab94c82ec9c6544d11ab7eec3d911a94d929b
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: b65b9f8d6854572a84a5c69615152b63371395f0c5dcd6729c45789052296df54314db2bc3e977df41705eacb8bc79c247cee139a63fa695192f95816ed528ad
  languageName: node
  linkType: hard

"websocket@npm:^1.0.32":
  version: 1.0.34
  resolution: "websocket@npm:1.0.34"
  dependencies:
    bufferutil: "npm:^4.0.1"
    debug: "npm:^2.2.0"
    es5-ext: "npm:^0.10.50"
    typedarray-to-buffer: "npm:^3.1.5"
    utf-8-validate: "npm:^5.0.2"
    yaeti: "npm:^0.0.6"
  checksum: b72e3dcc3fa92b4a4511f0df89b25feed6ab06979cb9e522d2736f09855f4bf7588d826773b9405fcf3f05698200eb55ba9da7ef333584653d4912a5d3b13c18
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^2.0.4":
  version: 2.0.4
  resolution: "whatwg-fetch@npm:2.0.4"
  checksum: 407d11d1761b3c2d15e3a92adcdeec73313fa0c0019aac59fdfac674d97f1a7014a8d7f4c9d3061d4a58ccb1c31d06a72e473d1716098007d356d94b01954a80
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: f95adbc1e80820828b45cc671d97da7cd5e4ef9deb426c31bcd5ab00dc7103042291613b3ef3caec0a2335ed09e0d5ed026c940755dbb6d404e2b27f940fdf07
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.11, which-typed-array@npm:^1.1.2":
  version: 1.1.13
  resolution: "which-typed-array@npm:1.1.13"
  dependencies:
    available-typed-arrays: "npm:^1.0.5"
    call-bind: "npm:^1.0.4"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.0"
  checksum: 605e3e10b7118af904a0e79d0d50b95275102f06ec902734024989cd71354929f7acee50de43529d3baf5858e2e4eb32c75e6ebd226c888ad976d8140e4a3e71
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^4.0.0":
  version: 4.0.0
  resolution: "which@npm:4.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: f17e84c042592c21e23c8195108cff18c64050b9efb8459589116999ea9da6dd1509e6a1bac3aeebefd137be00fabbb61b5c2bc0aa0f8526f32b58ee2f545651
  languageName: node
  linkType: hard

"workerpool@npm:6.2.1":
  version: 6.2.1
  resolution: "workerpool@npm:6.2.1"
  checksum: 3e637f76320cab92eaeffa4fbefb351db02e20aa29245d8ee05fa7c088780ef7b4446bfafff2668a22fd94b7d9d97c7020117036ac77a76370ecea278b9a9b91
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:7.4.6":
  version: 7.4.6
  resolution: "ws@npm:7.4.6"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 150e3f917b7cde568d833a5ea6ccc4132e59c38d04218afcf2b6c7b845752bd011a9e0dc1303c8694d3c402a0bdec5893661a390b71ff88f0fc81a4e4e66b09c
  languageName: node
  linkType: hard

"ws@npm:^3.0.0":
  version: 3.3.3
  resolution: "ws@npm:3.3.3"
  dependencies:
    async-limiter: "npm:~1.0.0"
    safe-buffer: "npm:~5.1.0"
    ultron: "npm:~1.1.0"
  checksum: 4b4a7e5d11025e399d82a7471bfb4818d563c892f5d953c2de937d262bd8e8acc8b340220001c01f8392574fccbc2df153d6031e285b8b38441187ea0c2cfd72
  languageName: node
  linkType: hard

"ws@npm:^5.1.1":
  version: 5.2.3
  resolution: "ws@npm:5.2.3"
  dependencies:
    async-limiter: "npm:~1.0.0"
  checksum: 5adfa5f636fdae931ac258c5a6d5d2de2d5b7430010625919a429ace4edf59d8de753266acf2410897966ef0391119900779963f5b9a40dd278337736df0f308
  languageName: node
  linkType: hard

"ws@npm:^7.4.6":
  version: 7.5.9
  resolution: "ws@npm:7.5.9"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 171e35012934bd8788150a7f46f963e50bac43a4dc524ee714c20f258693ac4d3ba2abadb00838fdac42a47af9e958c7ae7e6f4bc56db047ba897b8a2268cf7c
  languageName: node
  linkType: hard

"xhr-request-promise@npm:^0.1.2":
  version: 0.1.3
  resolution: "xhr-request-promise@npm:0.1.3"
  dependencies:
    xhr-request: "npm:^1.1.0"
  checksum: 49ec3474884858faa55349894b1879c872422a24485097c8b71ba9046027d27f1d54eb61dfdb9d72e78892c7371d22d9cc6a4e101b6767bb4df89a0b6d739f85
  languageName: node
  linkType: hard

"xhr-request@npm:^1.0.1, xhr-request@npm:^1.1.0":
  version: 1.1.0
  resolution: "xhr-request@npm:1.1.0"
  dependencies:
    buffer-to-arraybuffer: "npm:^0.0.5"
    object-assign: "npm:^4.1.1"
    query-string: "npm:^5.0.1"
    simple-get: "npm:^2.7.0"
    timed-out: "npm:^4.0.1"
    url-set-query: "npm:^1.0.0"
    xhr: "npm:^2.0.4"
  checksum: 531c5e1e47d2e680c1ae1296af7fa375d752cd83c3fa1f9bd9e82fc4fb305ce8e7aaf266256e82bbd34e2a4891ec535bcc4e9f8db2691ab64bb3b6ff40296b9a
  languageName: node
  linkType: hard

"xhr@npm:^2.0.4, xhr@npm:^2.2.0, xhr@npm:^2.3.3":
  version: 2.6.0
  resolution: "xhr@npm:2.6.0"
  dependencies:
    global: "npm:~4.4.0"
    is-function: "npm:^1.0.1"
    parse-headers: "npm:^2.0.0"
    xtend: "npm:^4.0.0"
  checksum: 31f34aba708955008c87bcd21482be6afc7ff8adc28090e633b1d3f8d3e8e93150bac47b262738b046d7729023a884b655d55cf34e9d14d5850a1275ab49fb37
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0, xtend@npm:^4.0.1, xtend@npm:~4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"xtend@npm:~2.1.1":
  version: 2.1.2
  resolution: "xtend@npm:2.1.2"
  dependencies:
    object-keys: "npm:~0.4.0"
  checksum: a8b79f31502c163205984eaa2b196051cd2fab0882b49758e30f2f9018255bc6c462e32a090bf3385d1bda04755ad8cc0052a09e049b0038f49eb9b950d9c447
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 5f1b5f95e3775de4514edbb142398a2c37849ccfaf04a015be5d75521e9629d3be29bd4432d23c57f37e5b61ade592fb0197022e9993f81a06a5afbdcda9346d
  languageName: node
  linkType: hard

"yaeti@npm:^0.0.6":
  version: 0.0.6
  resolution: "yaeti@npm:0.0.6"
  checksum: 6db12c152f7c363b80071086a3ebf5032e03332604eeda988872be50d6c8469e1f13316175544fa320f72edad696c2d83843ad0ff370659045c1a68bcecfcfea
  languageName: node
  linkType: hard

"yallist@npm:^3.0.0, yallist@npm:^3.0.2, yallist@npm:^3.1.1":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 9af0a4329c3c6b779ac4736c69fae4190ac03029fa27c1aef4e6bcc92119b73dea6fe5db5fe881fb0ce2a0e9539a42cdf60c7c21eda04d1a0b8c082e38509efb
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yargs-parser@npm:20.2.4":
  version: 20.2.4
  resolution: "yargs-parser@npm:20.2.4"
  checksum: db8f251ae40e24782d5c089ed86883ba3c0ce7f3c174002a67ec500802f928df9d505fea5d04829769221ce20b0f69f6fb1138fbb2e2fb102e3e9d426d20edab
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 0188f430a0f496551d09df6719a9132a3469e47fe2747208b1dd0ab2bb0c512a95d0b081628bbca5400fb20dbf2fabe63d22badb346cecadffdd948b049f3fcc
  languageName: node
  linkType: hard

"yargs-unparser@npm:2.0.0":
  version: 2.0.0
  resolution: "yargs-unparser@npm:2.0.0"
  dependencies:
    camelcase: "npm:^6.0.0"
    decamelize: "npm:^4.0.0"
    flat: "npm:^5.0.2"
    is-plain-obj: "npm:^2.1.0"
  checksum: 68f9a542c6927c3768c2f16c28f71b19008710abd6b8f8efbac6dcce26bbb68ab6503bed1d5994bdbc2df9a5c87c161110c1dfe04c6a3fe5c6ad1b0e15d9a8a3
  languageName: node
  linkType: hard

"yargs@npm:16.2.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: "npm:^7.0.2"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.0"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^20.2.2"
  checksum: 807fa21211d2117135d557f95fcd3c3d390530cda2eca0c840f1d95f0f40209dcfeb5ec18c785a1f3425896e623e3b2681e8bb7b6600060eda1c3f4804e7957e
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zksync-web3@npm:^0.14.3":
  version: 0.14.4
  resolution: "zksync-web3@npm:0.14.4"
  peerDependencies:
    ethers: ^5.7.0
  checksum: a1566a2a2ba34a3026680f3b4000ffa02593e02d9c73a4dd143bde929b5e39b09544d429bccad0479070670cfdad5f6836cb686c4b8d7954b4d930826be91c92
  languageName: node
  linkType: hard
